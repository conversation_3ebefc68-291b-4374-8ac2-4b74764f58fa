import glob
import os
import json
from abc import abstractmethod
from itertools import chain
from scipy.ndimage import zoom

import numpy as np

import pytorch3dunet.augment.transforms as transforms
from pytorch3dunet.datasets.utils import (
    get_slice_builder,
    ConfigDataset,
    calculate_stats,
    mirror_pad,
    decompress_ndarray,
)
from pytorch3dunet.unet3d.utils import get_logger

logger = get_logger("NumpyDataset")


def _create_padded_indexes(indexes, halo_shape):
    return tuple(
        slice(index.start, index.stop + 2 * halo)
        for index, halo in zip(indexes, halo_shape)
    )


def traverse_numpy_paths_old(raw_file_paths, label_file_paths):
    """
    Original traverse_numpy_paths function for backward compatibility.

    Args:
        raw_file_paths: List of raw file paths or directories
        label_file_paths: List of label file paths or directories, or None for test phase

    Returns:
        A tuple of (raw_file_paths, label_file_paths) where each is a list of file paths
    """
    import glob
    from itertools import chain

    assert isinstance(raw_file_paths, list)
    assert isinstance(label_file_paths, list) or label_file_paths is None
    raw_results = []
    label_results = []

    # 处理raw文件路径
    for file_path in raw_file_paths:
        if os.path.isdir(file_path):
            # if file path is a directory take all zst files in that directory
            files = glob.glob(os.path.join(file_path, "*.zst"))
            # 对文件按文件名排序
            files.sort()
            raw_results.append(files)  # 将文件列表添加到结果列表中
        else:
            raw_results.append([file_path])  # 将单个文件作为列表添加到结果列表中

    # if running test phase, return raw_results and label_results
    total_file_num = len(list(chain(*raw_results)))
    if label_file_paths is None:
        return [file for files in raw_results for file in files], [
            None
        ] * total_file_num

    # 处理label文件路径
    for file_path in label_file_paths:
        if os.path.isdir(file_path):
            # if file path is a directory take all zst files in that directory
            files = glob.glob(os.path.join(file_path, "*.zst"))
            # 对文件按文件名排序
            files.sort()
            label_results.append(files)  # 将文件列表添加到结果列表中
        else:
            label_results.append([file_path])  # 将单个文件作为列表添加到结果列表中

    # 检查raw和label文件数量是否匹配
    assert len(raw_results) == len(
        label_results
    ), "Raw and label directory counts must match"
    for raw_files, label_files in zip(raw_results, label_results):
        assert len(raw_files) == len(
            label_files
        ), "Raw and label file counts must match in each directory"

    # 将列表扁平化
    return [file for files in raw_results for file in files], [
        file for files in label_results for file in files
    ]


def traverse_numpy_paths(
    root_dir, normalization_file, datasets_info, split="train", mask_dir_name=None
):
    """
    Process dataset information in the format used by get_datasets function.

    Args:
        root_dir: Root directory containing the dataset
        normalization_file: Path to the normalization parameters file
        datasets_info: List of dataset information dictionaries
        split: Dataset split (train, val, test)

    Returns:
        A list of dataset dictionaries, each containing volume_path, masks_path, volume_mean, and volume_std
    """
    mask_dir_name = mask_dir_name or "seg"
    datasets = []
    with open(normalization_file, "r") as f:
        normalization_params = json.load(f)

    for dataset_info in datasets_info:
        name = dataset_info["name"]
        organelles = dataset_info["organelles"]
        volume_mean = normalization_params[name]["mean"]
        volume_std = normalization_params[name]["std"]
        for organelle in organelles:
            volume_dir = os.path.join(root_dir, split, name, "em", organelle["em"])
            masks_dir = os.path.join(
                root_dir, split, name, mask_dir_name, organelle["seg"]
            )

            volume_files = sorted(
                [
                    os.path.join(volume_dir, f)
                    for f in os.listdir(volume_dir)
                    if f.endswith(".zst")
                ]
            )
            masks_files = sorted(
                [
                    os.path.join(masks_dir, f)
                    for f in os.listdir(masks_dir)
                    if f.endswith(".zst")
                ]
            )

            if len(volume_files) != len(masks_files):
                raise ValueError("Number of volume and masks files should be the same")

            for volume_file, masks_file in zip(volume_files, masks_files):
                datasets.append(
                    {
                        "volume_path": volume_file,
                        "masks_path": masks_file,
                        "volume_mean": volume_mean,
                        "volume_std": volume_std,
                    }
                )

    return datasets


class AbstractZstdDataset(ConfigDataset):
    """
    Implementation of torch.utils.data.Dataset backed by the numpy arrays, which iterates over the raw and label datasets
    patch by patch with a given stride.

    Args:
        raw_file_path (str): path to numpy array containing raw data
        label_file_path (str): path to numpy array containing labels
        phase (str): 'train' for training, 'val' for validation, 'test' for testing
        slice_builder_config (dict): configuration of the SliceBuilder
        transformer_config (dict): data augmentation configuration
        global_normalization (bool): if True, the mean and std of the raw data will be calculated over the whole dataset
    """

    def __init__(
        self,
        raw_file_path,
        label_file_path,
        phase,
        slice_builder_config,
        transformer_config,
        raw_volume=None,
        label_volume=None,
        global_normalization=True,
        volume_mean=None,
        volume_std=None,
    ):
        assert phase in ["train", "val", "test"]

        raw = decompress_ndarray(raw_file_path) if raw_volume is None else raw_volume
        label = (
            (
                decompress_ndarray(label_file_path)
                if label_volume is None
                else label_volume
            )
            if phase != "test"
            else None
        )

        self.phase = phase
        self.raw_file_path = raw_file_path
        self.label_file_path = label_file_path

        self.halo_shape = slice_builder_config.get("halo_shape", [0, 0, 0])

        # Use provided normalization parameters if available
        if volume_mean is not None and volume_std is not None:
            logger.info(
                f"Using provided normalization parameters: mean={volume_mean}, std={volume_std}"
            )
            stats = {"mean": volume_mean, "std": volume_std}
        elif global_normalization:
            logger.info("Calculating mean and std of the raw data...")
            stats = calculate_stats(raw)
        else:
            stats = calculate_stats(None, True)

        self.transformer = transforms.Transformer(transformer_config, stats)
        self.raw_transform = self.transformer.raw_transform()

        if phase != "test":
            # create label transform only in train/val phase
            self.label_transform = self.transformer.label_transform()
            self._check_volume_sizes(raw, label)
        else:
            # 'test' phase used only for predictions so ignore the label dataset
            self.label = None

            # compare patch and stride configuration
            patch_shape = slice_builder_config.get("patch_shape")
            stride_shape = slice_builder_config.get("stride_shape")
            if sum(self.halo_shape) != 0 and patch_shape != stride_shape:
                logger.warning(
                    f"Found non-zero halo shape {self.halo_shape}."
                    f"In this case: patch shape and stride shape should be equal for optimal prediction "
                    f"performance, but found patch_shape: {patch_shape} and stride_shape: {stride_shape}!"
                )

        # build slice indices for raw and label data sets
        slice_builder = get_slice_builder(raw, label, None, slice_builder_config)
        self.raw_slices = slice_builder.raw_slices
        self.label_slices = slice_builder.label_slices

        self.patch_count = len(self.raw_slices)
        logger.info(f"Number of patches: {self.patch_count}")

    @abstractmethod
    def get_raw_patch(self, idx):
        raise NotImplementedError

    @abstractmethod
    def get_label_patch(self, idx):
        raise NotImplementedError

    @abstractmethod
    def get_raw_padded_patch(self, idx):
        raise NotImplementedError

    def volume_shape(self):
        raw = decompress_ndarray(self.raw_file_path)
        if raw.ndim == 3:
            return raw.shape
        else:
            return raw.shape[1:]

    def __getitem__(self, idx):
        if idx >= len(self):
            raise StopIteration

        raw_idx = self.raw_slices[idx]

        if self.phase == "test":
            if len(raw_idx) == 4:
                # discard the channel dimension in the slices: predictor requires only the spatial dimensions of the volume
                raw_idx = raw_idx[
                    1:
                ]  # Remove the first element if raw_idx has 4 elements
                raw_idx_padded = (slice(None),) + _create_padded_indexes(
                    raw_idx, self.halo_shape
                )
            else:
                raw_idx_padded = _create_padded_indexes(raw_idx, self.halo_shape)

            raw_patch_transformed = self.raw_transform(
                self.get_raw_padded_patch(raw_idx_padded)
            )
            return raw_patch_transformed, raw_idx
        else:
            raw_patch_transformed = self.raw_transform(self.get_raw_patch(raw_idx))

            # get the slice for a given index 'idx'
            label_idx = self.label_slices[idx]
            label_patch_transformed = self.label_transform(
                self.get_label_patch(label_idx)
            )

            # return the transformed raw and label patches
            return raw_patch_transformed, label_patch_transformed

    def __len__(self):
        return self.patch_count

    def _check_volume_sizes(self, raw=None, label=None):
        def _volume_shape(volume):
            if volume.ndim == 3:
                return volume.shape
            return volume.shape[1:]

        # raw = decompress_ndarray(self.raw_file_path)
        # label = decompress_ndarray(self.label_file_path)
        assert raw.ndim in [3, 4], "Raw dataset must be 3D (DxHxW) or 4D (CxDxHxW)"
        assert label.ndim in [3, 4], "Label dataset must be 3D (DxHxW) or 4D (CxDxHxW)"
        assert _volume_shape(raw) == _volume_shape(
            label
        ), "Raw and labels have to be of the same size"

    @classmethod
    def create_datasets(
        cls,
        dataset_config,
        phase,
        raw_volume=None,
        label_volume=None,
        volume_mean=None,
        volume_std=None,
    ):
        phase_config = dataset_config[phase]

        # load data augmentation configuration
        transformer_config = phase_config["transformer"]
        # load slice builder config
        slice_builder_config = phase_config["slice_builder"]
        # Get global normalization setting
        global_normalization = dataset_config.get("global_normalization", True)
        # Check if resolution is provided
        resolution = dataset_config.get("resolution", None)
        using_sdf = dataset_config.get("using_sdf", False)

        datasets = []

        # Check if we're using raw_volume directly
        if raw_volume is not None:
            try:
                logger.info(f"Loading {phase} set from raw_volume ...")
                dataset = cls(
                    raw_file_path=None,
                    label_file_path=None,
                    phase=phase,
                    slice_builder_config=slice_builder_config,
                    transformer_config=transformer_config,
                    raw_volume=raw_volume,
                    label_volume=label_volume,
                    global_normalization=global_normalization,
                    resolution=resolution,
                    using_sdf=using_sdf,
                    volume_mean=volume_mean,
                    volume_std=volume_std,
                )
                datasets.append(dataset)
            except Exception:
                logger.error(
                    f"Skipping raw_volume dataset",
                    exc_info=True,
                )
            return datasets

        # Check if we're using the new dataset format
        if "config" in dataset_config:
            path_phase = phase
            phase_path_config = dataset_config["config"].get(phase, None)

            # Handle special cases.
            if phase_path_config is None and phase == "test":
                logger.warning(
                    f"No configuration found for phase: {phase}. Use val instead."
                )
                path_phase = "val"
                phase_path_config = dataset_config["config"].get("val")

            # Using the new format from get_datasets
            root_dir = phase_path_config["root_dir"]
            mask_dir_name = phase_path_config["mask_dir_name"]
            normalization_file = phase_path_config["normalization_file"]
            datasets_info = phase_path_config["datasets_info"]

            # Get dataset information including normalization parameters
            dataset_list = traverse_numpy_paths(
                root_dir, normalization_file, datasets_info, path_phase, mask_dir_name
            )

            # Create datasets from the dataset information
            for dataset_info in dataset_list:
                try:
                    raw_file_path = dataset_info["volume_path"]
                    label_file_path = dataset_info["masks_path"]
                    volume_mean = dataset_info["volume_mean"]
                    volume_std = dataset_info["volume_std"]

                    logger.info(
                        f"Loading {phase} set from: {raw_file_path} and {label_file_path} "
                        f"with normalization: mean={volume_mean}, std={volume_std}"
                    )

                    dataset = cls(
                        raw_file_path=raw_file_path,
                        label_file_path=label_file_path,
                        phase=phase,
                        slice_builder_config=slice_builder_config,
                        transformer_config=transformer_config,
                        global_normalization=False,  # Don't use global normalization when we have specific parameters
                        volume_mean=volume_mean,
                        volume_std=volume_std,
                        using_sdf=using_sdf,
                        resolution=resolution,
                    )
                    datasets.append(dataset)
                except Exception:
                    logger.error(
                        f"Skipping dataset: {dataset_info}",
                        exc_info=True,
                    )
        else:
            # Using the old format
            raw_file_paths = phase_config.get("raw_file_paths", [])
            label_file_paths = (
                phase_config.get("label_file_paths", []) if phase != "test" else None
            )

            if raw_file_paths:
                raw_file_paths, label_file_paths = traverse_numpy_paths_old(
                    raw_file_paths, label_file_paths
                )

                # No raw file paths provided
                if not raw_file_paths:
                    logger.warning(f"No raw file paths found for {phase} phase")
                    return datasets

                # Create datasets from raw and label file paths
                for raw_file_path, label_file_path in zip(
                    raw_file_paths, label_file_paths
                ):
                    try:
                        logger.info(
                            f"Loading {phase} set from: {raw_file_path} and {label_file_path} ..."
                        )
                        dataset = cls(
                            raw_file_path=raw_file_path,
                            label_file_path=label_file_path,
                            phase=phase,
                            slice_builder_config=slice_builder_config,
                            transformer_config=transformer_config,
                            global_normalization=global_normalization,
                        )
                        datasets.append(dataset)
                    except Exception:
                        logger.error(
                            f"Skipping {phase} set: {raw_file_path} and {label_file_path}",
                            exc_info=True,
                        )

        return datasets


def resize_volume(volume: np.ndarray, resolution: int, order=1, scale_value=False):
    """
    Resize the volume to the specified resolution using zoom.

    Args:
        volume (numpy.ndarray): The input volume to resize.
        resolution (int): The target resolution for resizing.
        order (int): The order of the spline interpolation used by zoom.
            Default is 1 (linear interpolation).
        scale_value (bool): Whether to scale the volume values by the zoom factor.
            Default is False.

    Returns:
        numpy.ndarray: The resized volume.
    """
    # Convert resolution to a tuple
    resolution = (resolution, resolution, resolution)
    zoom_factor = np.array(resolution) / np.array(volume.shape)

    if volume.dtype == np.float16:
        # Convert to float32 for zoom operation
        volume = volume.astype(np.float32)

    # If zoom_factor is close to 1, return the original volume
    if np.allclose(zoom_factor, 1):
        return volume

    if scale_value:
        # Scale the volume values by the zoom factor
        assert zoom_factor[0] == zoom_factor[1] == zoom_factor[2]
        volume = volume * zoom_factor[0]
        logger.info(
            f"Scaling volume values by zoom factor: {zoom_factor} for resolution: {resolution}"
        )

    return zoom(volume, zoom_factor, order=order)


class StandardZstdDataset(AbstractZstdDataset):
    """
    Implementation of the numpy dataset which loads the data from the numpy arrays into the memory.
    Fast but might consume a lot of memory.
    """

    def __init__(
        self,
        raw_file_path,
        label_file_path,
        phase,
        slice_builder_config,
        transformer_config,
        raw_volume=None,
        label_volume=None,
        global_normalization=True,
        resolution=None,
        using_sdf=False,
        volume_mean=None,
        volume_std=None,
    ):
        raw = decompress_ndarray(raw_file_path) if raw_volume is None else raw_volume
        label = (
            (
                decompress_ndarray(label_file_path)
                if label_volume is None
                else label_volume
            )
            if phase != "test"
            else None
        )
        if resolution is not None:
            raw = resize_volume(raw, resolution, order=1)
            if label is not None:
                if using_sdf:
                    label = resize_volume(label, resolution, order=1, scale_value=True)
                else:
                    label = resize_volume(label, resolution, order=0)
        super().__init__(
            raw_file_path=raw_file_path,
            label_file_path=label_file_path,
            phase=phase,
            slice_builder_config=slice_builder_config,
            transformer_config=transformer_config,
            raw_volume=raw,
            label_volume=label,
            global_normalization=global_normalization,
            volume_mean=volume_mean,
            volume_std=volume_std,
        )
        self._raw = raw
        if phase == "test":
            self._raw_padded = mirror_pad(self._raw, self.halo_shape)
        else:
            self._raw_padded = None
        self._label = label

    def volume_shape(self):
        raw = self._raw
        if raw.ndim == 3:
            return raw.shape
        else:
            return raw.shape[1:]

    def get_raw_patch(self, idx):
        if self._raw is None:
            raise RuntimeError("Raw data is not loaded")
        return self._raw[idx]

    def get_label_patch(self, idx):
        if self._label is None:
            raise RuntimeError("Label data is not loaded")
        return self._label[idx]

    def get_raw_padded_patch(self, idx):
        if self._raw_padded is None:
            raise RuntimeError("Raw data is not loaded")
        return self._raw_padded[idx]


class LazyNumpyDataset(AbstractZstdDataset):
    """Implementation of the numpy dataset which loads the data lazily.
    It's slower, but has a low memory footprint."""

    def __init__(
        self,
        raw_file_path,
        label_file_path,
        phase,
        slice_builder_config,
        transformer_config,
        global_normalization=False,
        volume_mean=None,
        volume_std=None,
    ):
        super().__init__(
            raw_file_path=raw_file_path,
            label_file_path=label_file_path,
            phase=phase,
            slice_builder_config=slice_builder_config,
            transformer_config=transformer_config,
            global_normalization=global_normalization,
            volume_mean=volume_mean,
            volume_std=volume_std,
        )

        logger.info("Using LazyNumpyDataset")

    def get_raw_patch(self, idx):
        return decompress_ndarray(self.raw_file_path)[idx]

    def get_label_patch(self, idx):
        return decompress_ndarray(self.label_file_path)[idx]

    def get_raw_padded_patch(self, idx):
        raw = decompress_ndarray(self.raw_file_path)[:]
        raw_padded = mirror_pad(raw, self.halo_shape)
        return raw_padded[idx]
