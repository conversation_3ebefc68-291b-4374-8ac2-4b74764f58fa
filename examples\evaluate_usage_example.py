#!/usr/bin/env python3
"""
Example usage of the refactored evaluate.py module.
Demonstrates both specific format and generic format usage.
"""

import os
import sys

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from predict.evaluate import get_evaluation_files, evaluate_instance_segmentation


def example_specific_format():
    """Example using specific format (mito_seg)."""
    print("=== Example: Specific Format (mito_seg) ===")
    
    # Example paths
    pred_dir = "/path/to/predictions"  # Contains files like: pred_001_gaussian.zst
    true_dir = "/path/to/ground_truth"  # Contains files like: mito_seg_001.zst
    
    # Get evaluation files using specific format
    files_to_evaluate = get_evaluation_files(pred_dir, true_dir, use_specific_format=True)
    
    print(f"Found {len(files_to_evaluate)} files to evaluate")
    for file_info in files_to_evaluate:
        print(f"  Pred: {file_info['pred_path']}")
        print(f"  True: {file_info['true_path']}")
        print(f"  ID: {file_info['idx']}, Suffix: {file_info['surfix']}")
        print()
    
    # The resulting metrics_all structure will be:
    # metrics_all[idx][surfix] = metrics
    # Example: metrics_all["001"]["gaussian"] = {"mAP": 0.85, "precision": 0.90, ...}


def example_generic_format():
    """Example using generic format."""
    print("=== Example: Generic Format ===")
    
    # Example paths
    pred_dir = "/path/to/predictions"  # Contains files like: volume1.zst, volume2.npz
    true_dir = "/path/to/ground_truth"  # Contains files like: gt1.zst, gt2.npz
    
    # Get evaluation files using generic format
    files_to_evaluate = get_evaluation_files(pred_dir, true_dir, use_specific_format=False)
    
    print(f"Found {len(files_to_evaluate)} files to evaluate")
    for file_info in files_to_evaluate:
        print(f"  Pred: {file_info['pred_path']}")
        print(f"  True: {file_info['true_path']}")
        print(f"  ID: {file_info['idx']}, Suffix: {file_info['surfix']}")  # Both will be None
        print()
    
    # The resulting metrics_all structure will be:
    # metrics_all[pred_path] = metrics
    # Example: metrics_all["/path/to/predictions/volume1.zst"] = {"mAP": 0.85, ...}


def example_evaluate_instance_segmentation():
    """Example of evaluating instance segmentation with different formats."""
    print("=== Example: Instance Segmentation Evaluation ===")
    
    pred_dir = "/path/to/predictions"
    true_dir = "/path/to/ground_truth"
    
    # Example 1: Using specific format
    print("1. Using specific format:")
    metrics_specific = evaluate_instance_segmentation(
        pred_dir=pred_dir,
        true_dir=true_dir,
        iou_threshold=0.5,
        use_chunks=True,
        num_chunks=8,
        n_jobs=-1,
        use_gpu=False,
        use_specific_format=True  # Use mito_seg format
    )
    
    # Access results: metrics_specific[idx][surfix]
    # for idx, metrics_by_suffix in metrics_specific.items():
    #     for suffix, metrics in metrics_by_suffix.items():
    #         print(f"ID {idx}, Suffix {suffix}: mAP = {metrics['mAP']:.4f}")
    
    print("2. Using generic format:")
    metrics_generic = evaluate_instance_segmentation(
        pred_dir=pred_dir,
        true_dir=true_dir,
        iou_threshold=0.5,
        use_chunks=True,
        num_chunks=8,
        n_jobs=-1,
        use_gpu=False,
        use_specific_format=False  # Use generic format
    )
    
    # Access results: metrics_generic[pred_path]
    # for pred_path, metrics in metrics_generic.items():
    #     if pred_path != "average":  # Skip the average metrics
    #         print(f"File {pred_path}: mAP = {metrics['mAP']:.4f}")


def example_config_usage():
    """Example of using the new parameter in config files."""
    print("=== Example: Config File Usage ===")
    
    config_example = """
# config/config.yaml

# Evaluation settings
instance_mode: true
iou_threshold: 0.5
use_specific_format: true  # Set to false for generic format

# Parallel processing settings
use_chunks: true
num_chunks: 8
n_jobs: -1
use_gpu: false

# Paths
pred_dir: "${output_root}/gaussian_blur"
true_dir: "${datasets_root}/mito_seg/val"
"""
    
    print("Add this to your config file:")
    print(config_example)
    
    print("Then run evaluation with:")
    print("python predict/evaluate.py")


if __name__ == "__main__":
    print("Refactored Evaluate Module Usage Examples")
    print("=" * 50)
    
    example_specific_format()
    print()
    
    example_generic_format()
    print()
    
    example_evaluate_instance_segmentation()
    print()
    
    example_config_usage()
    
    print("\nKey Benefits of the Refactor:")
    print("1. Eliminated code duplication between evaluate_instance_segmentation and main")
    print("2. Added support for generic file pairing (sorted pred_dir and true_dir files)")
    print("3. Flexible data structure: metrics_all[idx][surfix] or metrics_all[pred_path]")
    print("4. Backward compatible with existing specific format")
    print("5. Easy to extend for other file naming conventions")
