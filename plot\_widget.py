# napari_sam3d/_widget.py

import os
import numpy as np
from typing import Optional
from pathlib import Path

import napari
from napari.types import LayerDataTuple
from napari.layers import Image, Points, Shapes, Labels
from napari.utils.notifications import show_info
from magicgui import magic_factory
from qtpy.QtWidgets import QWidget, QVBoxLayout, QPushButton, QFileDialog, QButtonGroup
from qtpy.QtCore import QThread, Signal, Qt

from hydra import initialize_config_dir, compose
from omegaconf import OmegaConf
from predict.config_manager import ConfigManager
from predict.adapter import FewshotAdapter, load_config
from hydra import initialize
from dataprocess.volume import Volume  # 根据实际文件位置调整导入路径
import traceback  # 确保导入

# 在文件顶部添加导入
from qtpy.QtWidgets import QListWidget, QListWidgetItem, QHBoxLayout, QLabel
from qtpy.QtGui import QColor

# # 添加方向到维度的映射
# DIRECTION_TO_DIM = {"z": 0, "y": 1, "x": 2}


class Prediction:

    def __init__(
        self, adapter: FewshotAdapter, volume: np.ndarray, prompts: dict, current_obj_id
    ):
        super().__init__()
        self.adapter = adapter
        self.volume = volume
        self.prompts = prompts  # 存储临时收集的prompts
        self.current_obj_id = current_obj_id
        # self.result = None  # 存储预测结果
        self.condition_frames = {}  # 存储生成的条件帧

    def generate_condition_frames(self):
        """为所有有提示的方向生成条件帧"""
        # 预处理
        self._prepare_prediction()

        # 为每个方向生成条件帧
        for direction in ["x", "y", "z"]:
            if direction not in self.prompts:
                continue

            # 应用当前方向的提示
            for frame_index, points in (
                self.prompts[direction].get("points", {}).items()
            ):
                for point in points:
                    coord, label = point
                    # 根据方向调整坐标
                    self.adapter.add_point_prompt(
                        frame_index, self.current_obj_id, coord, label, direction
                    )

            for frame_index, boxes in self.prompts[direction].get("boxes", {}).items():
                for box in boxes:
                    self.adapter.add_box_prompt(
                        frame_index, self.current_obj_id, box, direction
                    )

            for frame_index, mask_data in (
                self.prompts[direction].get("masks", {}).items()
            ):
                if np.any(mask_data):
                    self.adapter.add_mask_prompt(
                        frame_index, self.current_obj_id, mask_data, direction
                    )

            # 为当前方向生成条件帧
            self.condition_frames[direction] = {}
            for frame_index in set(
                list(self.prompts[direction].get("points", {}).keys())
                + list(self.prompts[direction].get("boxes", {}).keys())
                + list(self.prompts[direction].get("masks", {}).keys())
            ):
                condition_mask = self.adapter.predict_single_frame(
                    frame_index, direction
                )
                self.condition_frames[direction][frame_index] = condition_mask

        return self.condition_frames

    def run_propagation(self):
        """使用条件帧执行三维传播预测"""
        if not self.condition_frames:
            raise ValueError("No condition frames available. Generate them first.")

        # 确保适配器已加载体积数据
        if not self.adapter.working_volume:
            self._prepare_prediction()

        # 设置所有方向的条件帧
        self.adapter.condition_frames = self.condition_frames

        # 执行传播预测
        return self.adapter.predict()

    def _prepare_prediction(self):
        """将收集的prompts应用到适配器"""
        # 加载体积数据
        volume_obj = Volume()
        volume_obj.set_volume(self.volume)
        if volume_obj.volume.dtype != np.uint8 and volume_obj.volume.dtype != np.uint16:
            print(f"Converting volume from {volume_obj.volume.dtype} to uint8")
            volume_obj.convert_float32_to_uint8()
        self.adapter.load_volume_to_predictor(volume_obj)


class Sam3DWidget(QWidget):
    def __init__(self, viewer: napari.Viewer):
        super().__init__()
        self.viewer = viewer
        self.current_obj_id = 1  # 当前操作的对象ID

        self.predictor = self._init_predictor()
        self.prompts = {
            "x": {
                "points": {},  # {frame_index: [(coord, label), ...]}
                "boxes": {},  # {frame_index: [box, ...]}
                "masks": {},  # {frame_index: mask_data (2D数组)}
            },
            "y": {"points": {}, "boxes": {}, "masks": {}},
            "z": {"points": {}, "boxes": {}, "masks": {}},
        }
        self.current_frame = self.viewer.dims.current_step[0]  # 当前帧索引
        self.current_direction = "z"  # 默认当前方向为z轴
        self.points_layer = None
        self.boxes_layer = None
        # 修改：为每个方向创建独立的掩码图层
        self.masks_layers = {"x": None, "y": None, "z": None}
        self.prompted_frames = set()  # 存储有提示的帧和方向 {(direction, frame_index)}
        # 预初始化体积图层（最底层）
        if "3d_volume" not in self.viewer.layers:
            self.viewer.add_image(
                np.zeros((1, 1, 1)),  # 初始占位数据
                name="3d_volume",
                visible=False,  # 初始不可见，加载数据后再显示
            )
        self._create_prompt_layers()
        self._setup_ui()
        self._connect_events()
        if self.predictor is None:
            self._disable_buttons()  # 禁用所有功能按钮
            show_info("Initialization failed! Check configuration.")
        self.prompts_history = []  # 存储历史记录 [{z: int, type: str, data: object}]
        self.history_items = {}  # 存储历史列表项 {(direction, frame_index): QListWidgetItem}

    def _disable_buttons(self):
        self.btn_load_model.setEnabled(False)
        self.btn_predict.setEnabled(False)
        self.btn_save.setEnabled(False)

    def _setup_ui(self):
        main_layout = QVBoxLayout()
        self.setLayout(main_layout)

        # 方向选择区域
        direction_layout = QHBoxLayout()
        direction_layout.addWidget(QLabel("Direction:"))

        # # 方向选择下拉菜单
        # self.direction_combo = QComboBox()
        # self.direction_combo.addItems(['x', 'y', 'z'])
        # self.direction_combo.setCurrentText('z')
        # self.direction_combo.currentTextChanged.connect(self._on_direction_changed)
        # direction_layout.addWidget(self.direction_combo)

        # main_layout.addLayout(direction_layout)

        # 操作按钮区域
        button_layout = QVBoxLayout()
        self.btn_load_volume = QPushButton("Load Volume")
        button_layout.addWidget(self.btn_load_volume)

        self.btn_load_model = QPushButton("Load Model")
        button_layout.addWidget(self.btn_load_model)

        # 新增：生成条件帧按钮
        self.btn_generate_conditions = QPushButton("Generate Condition Frames")
        button_layout.addWidget(self.btn_generate_conditions)

        # 新增：执行传播预测按钮
        self.btn_run_propagation = QPushButton("Run Propagation")
        button_layout.addWidget(self.btn_run_propagation)

        self.btn_save = QPushButton("Save Result")
        button_layout.addWidget(self.btn_save)

        main_layout.addLayout(button_layout)

        # 历史记录区域
        history_layout = QVBoxLayout()
        history_layout.addWidget(QLabel("Prompt History:"))

        # 创建历史记录列表
        self.history_list = QListWidget()
        self.history_list.setMinimumHeight(150)
        self.history_list.itemClicked.connect(self._on_history_item_clicked)
        history_layout.addWidget(self.history_list)

        # 添加清除历史按钮
        self.btn_clear_history = QPushButton("Clear History")
        self.btn_clear_history.clicked.connect(self._clear_history)
        history_layout.addWidget(self.btn_clear_history)

        main_layout.addLayout(history_layout)

    def _connect_events(self):
        self.btn_load_volume.clicked.connect(self._load_volume)
        self.btn_load_model.clicked.connect(self._load_model)
        self.btn_generate_conditions.clicked.connect(self._generate_condition_frames)
        self.btn_run_propagation.clicked.connect(self._run_propagation)
        self.btn_save.clicked.connect(self._save_result)
        self.viewer.dims.events.current_step.connect(self._on_frame_change)
        self.viewer.dims.events.order.connect(self._on_direction_change)

        # 当点图层数据变化时
        self.points_layer.events.data.connect(
            lambda e: self._on_prompt_added("point")
        )

        # 当点图层属性变化时（如颜色）
        self.points_layer.events.properties.connect(
            lambda e: self._on_prompt_added("point")
        )

        # 当框图层数据变化时
        self.boxes_layer.events.data.connect(
            lambda e: self._on_prompt_added("box")
        )

        # 当掩码图层数据变化时
        for layer in self.masks_layers.values():
            if layer:
                layer.events.data.connect(lambda e: self._on_prompt_added("mask"))

        # # 当掩码图层数据变化时
        # self.masks_layer.events.data.connect(
        #     lambda e: self._add_to_history_for_current_layer("mask")
        # )

    def _load_volume(self, path=None):
        """加载体积数据"""
        if not path:
            path, _ = QFileDialog.getOpenFileName(
                self,
                "Select Volume File",
                "",
                "Volume files (*.zst *.zarr *.nii *.nii.gz)",
            )

        if not path:
            return

        # 使用Volume类加载数据
        vol_loader = Volume(path)
        vol_loader.load()
        image_data = vol_loader.volume.astype(np.float32)
        # 更新掩码图层形状
        self._update_mask_layer_shape(image_data.shape)
        # 添加到napari
        # if "3d_volume" in self.viewer.layers:
        #     self.viewer.layers["3d_volume"].data = image_data
        # else:
        #     self.viewer.add_image(
        #         image_data,
        #         name="3d_volume",
        #         contrast_limits=(image_data.min(), image_data.max()),
        #     )
        # self.viewer.layers["3d_volume"].z_index = 10
        if "3d_volume" in self.viewer.layers:
            layer = self.viewer.layers["3d_volume"]
            layer.data = image_data
            layer.contrast_limits = (image_data.min(), image_data.max())
            self.viewer.layers["3d_volume"].visible = True  # 加载后设为可见
        else:
            layer = self.viewer.add_image(
                image_data,
                name="3d_volume",
                contrast_limits=(image_data.min(), image_data.max()),
            )
        # 确保体积数据在最底层
        layer.z_index = 1  # 设置为最小值
        show_info("Volume loaded successfully")

    def _on_direction_change(self, event):
        """
        处理维度顺序变化事件的回调函数。
        从事件源中获取当前维度顺序元组，将切片方向映射到xyz空间坐标轴。
        """
        # 从事件源获取当前维度顺序元组
        order_tuple = event.source.order
        volumelayer = self.viewer.layers["3d_volume"]
        points_layer = self.viewer.layers["points_prompts"]
        # 获取当前切片方向的维度索引（最后一个元素）
        current_dim = order_tuple[0]


        self._save_current_frame_prompts_if_needed(self.current_frame)
        # 将维度索引映射到xyz空间坐标轴
        # 注意：Napari中默认维度顺序是(0,1,2)对应(z,y,x)，
        # 但用户可能改变顺序，所以需要根据实际索引映射
        if current_dim == 0:
            self.current_direction = "z"  # 通常第0维是z轴（深度）
        elif current_dim == 1:
            self.current_direction = "y"  # 通常第1维是y轴（垂直）
        elif current_dim == 2:
            self.current_direction = "x"  # 通常第2维是x轴（水平）
        else:
            # 处理更高维度的情况
            self.current_direction = "z"  # 默认设为z轴

        print(f"Current slicing direction: {self.current_direction}")

        # 隐藏当前方向的掩码图层
        if (
            self.current_direction in self.masks_layers
            and self.masks_layers[self.current_direction]
        ):
            self.masks_layers[self.current_direction].visible = False

        # 显示新方向的掩码图层
        if (
            self.current_direction in self.masks_layers
            and self.masks_layers[self.current_direction]
        ):
            self.masks_layers[self.current_direction].visible = True

        # 更新提示显示
        self._update_prompts_display()

    def _on_frame_change(self, event):
        """当帧变化时更新提示显示"""
        new_frame = event.value[0]
        if new_frame == self.current_frame:
            return

        # 修改：只在当前帧有提示时才保存
        if self._has_prompts(self.current_frame):
            self._save_current_frame_prompts_if_needed(self.current_frame)

        # 更新当前帧
        self.current_frame = new_frame

        # 更新提示显示
        self._update_prompts_display()

    def _on_prompt_added(self, prompt_type: str):
        """当添加新提示时调用"""
        # 添加当前帧到历史记录
        self._add_to_history(self.current_direction, self.current_frame)
        
        # 标记当前帧有未保存的提示
        self.has_unsaved_prompts = True

    def _has_prompts(self, frame_index: int) -> bool:
        """检查当前帧在当前方向是否有提示"""
        direction = self.current_direction
        
        # 检查点提示
        if frame_index in self.prompts[direction]["points"] and self.prompts[direction]["points"][frame_index]:
            return True
            
        # 检查框提示
        if frame_index in self.prompts[direction]["boxes"] and self.prompts[direction]["boxes"][frame_index]:
            return True
            
        # 检查掩码提示
        if frame_index in self.prompts[direction]["masks"] and np.any(self.prompts[direction]["masks"][frame_index]):
            return True
            
        return False
    
    def _save_current_frame_prompts_if_needed(self, frame_index: int):
        """只在有未保存提示时保存当前帧的提示"""
        if not hasattr(self, 'has_unsaved_prompts') or not self.has_unsaved_prompts:
            return
            
        direction = self.current_direction
        has_prompts = False  # 标记当前帧是否有任何提示

        # 检查点提示
        points_data = []
        if len(self.points_layer.data) > 0:
            has_prompts = True
            points = self.points_layer.data
            colors = self.points_layer.face_color

            for i, point in enumerate(points):
                label = 1 if colors[i][0] > 0.5 else 0
                points_data.append((point, label))
        
        # 检查框提示
        boxes_data = []
        for shape in self.boxes_layer.data:
            if len(shape) >= 2:  # 确保有足够的点形成框
                has_prompts = True
                min_coord = np.min(shape, axis=0)
                max_coord = np.max(shape, axis=0)
                box = [min_coord[0], min_coord[1], max_coord[0], max_coord[1]]
                boxes_data.append(box)

        # 检查掩码提示
        mask_data = None
        if (
            self.masks_layers[direction]
            and self.masks_layers[direction].data.shape[0] > frame_index
        ):
            # 根据方向获取掩码数据
            if direction == "z":  # z方向
                mask_slice = self.masks_layers[direction].data[frame_index, :, :]
            elif direction == "y":  # y方向
                mask_slice = self.masks_layers[direction].data[:, frame_index, :]
            elif direction == "x":  # x方向
                mask_slice = self.masks_layers[direction].data[:, :, frame_index].T
            
            if np.any(mask_slice != 0):  # 只保存非空掩码
                has_prompts = True
                mask_data = mask_slice

        # 只有当有提示时才保存
        if has_prompts:
            # 保存点提示
            if points_data:
                self.prompts[direction]["points"][frame_index] = points_data
            elif frame_index in self.prompts[direction]["points"]:
                del self.prompts[direction]["points"][frame_index]  # 清除空点提示
            
            # 保存框提示
            if boxes_data:
                self.prompts[direction]["boxes"][frame_index] = boxes_data
            elif frame_index in self.prompts[direction]["boxes"]:
                del self.prompts[direction]["boxes"][frame_index]  # 清除空框提示
            
            # 保存掩码提示
            if mask_data is not None:
                self.prompts[direction]["masks"][frame_index] = mask_data
            elif frame_index in self.prompts[direction]["masks"]:
                del self.prompts[direction]["masks"][frame_index]  # 清除空掩码提示
            
            # 添加到历史记录（如果尚未记录）
            #self._add_to_history(direction, frame_index)
        else:
            # 如果没有任何提示，清除所有相关条目
            for prompt_type in ["points", "boxes", "masks"]:
                if frame_index in self.prompts[direction][prompt_type]:
                    del self.prompts[direction][prompt_type][frame_index]
            
            # 从历史记录中移除
            self._remove_from_history(direction, frame_index)
        
        # 重置未保存标记
        self.has_unsaved_prompts = False
    def _remove_from_history(self, direction: str, frame_index: int):
        """从历史记录中移除指定帧"""
        key = (direction, frame_index)
        
        # 从提示帧集合中移除
        if key in self.prompted_frames:
            self.prompted_frames.remove(key)
        
        # 从历史列表项中移除
        if key in self.history_items:
            item = self.history_items[key]
            row = self.history_list.row(item)
            self.history_list.takeItem(row)
            del self.history_items[key]
    def _update_prompts_display(self):
        """更新当前帧的提示显示"""
        direction = self.current_direction
        frame_index = self.current_frame

        # 清除当前显示
        self.points_layer.data = []
        self.boxes_layer.data = []

        # 显示当前帧的点提示
        if frame_index in self.prompts[direction]["points"]:
            for coord, label in self.prompts[direction]["points"][frame_index]:
                self.points_layer.add(coord)

        # 显示当前帧的框提示
        if frame_index in self.prompts[direction]["boxes"]:
            for box in self.prompts[direction]["boxes"][frame_index]:
                # 创建矩形框
                rect_points = [[box[0], box[1]], [box[2], box[3]]]  # 左上角  # 右下角
                self.boxes_layer.add_rectangles(rect_points)

        # # 显示当前帧的掩码提示
        # if frame_index in self.prompts[direction]["masks"]:
        #     new_mask_data = self.masks_layer.data.copy()
        #     new_mask_data[frame_index] = self.prompts[direction]["masks"][frame_index]
        #     self.masks_layer.data = new_mask_data
        # else:
        #     new_mask_data = self.masks_layer.data.copy()
        #     new_mask_data[frame_index] = np.zeros_like(new_mask_data[frame_index])
        #     self.masks_layer.data = new_mask_data

    def _init_predictor(self) -> Optional[FewshotAdapter]:

        try:
            # 主动初始化 hydra
            initialize(config_path="../config", version_base=None)
            config_manager = load_config()

            return FewshotAdapter(config_manager=config_manager)
        except Exception as e:
            # 获取完整错误堆栈
            error_trace = traceback.format_exc()

            # 构造详细错误信息
            error_msg = (
                "🚨 Initialization Failed!\n"
                f"- Error Type: {type(e).__name__}\n"
                f"- Message: {str(e)}\n"
                "-----------------------\n"
                f"Stack Trace:\n{error_trace}\n"
            )

            # 显示到GUI和打印到控制台
            show_info(error_msg)
            print(error_msg)  # 确保控制台也有输出
            show_info(f"Initialization failed: {str(e)}")

            return None

    def _load_model(self):
        path, _ = QFileDialog.getOpenFileName(
            self, "Select Model File", "", "PyTorch Models (*.pt *.pth)"
        )
        if path:
            try:
                # 合并模型路径到现有配置
                loaded_cfg = OmegaConf.create({"model": {"checkpoint_path": path}})
                merged_cfg = OmegaConf.merge(self.predictor.cfg, loaded_cfg)

                # 重新创建配置管理器和预测器
                config_manager = ConfigManager.from_hydra_config(merged_cfg)
                self.predictor = FewshotAdapter(config_manager=config_manager)
                show_info("Model loaded successfully")
            except Exception as e:
                show_info(f"Model loading failed: {str(e)}")


    def _add_to_history(self, direction: str, frame_index: int):
        """添加有提示的帧到历史记录"""
        key = (direction, frame_index)
        
        # 如果已经记录过，跳过
        if key in self.prompted_frames:
            return
            
        # 添加到记录
        self.prompted_frames.add(key)
        
        # 创建列表项
        item_text = f"{direction.upper()}-{frame_index}"
        list_item = QListWidgetItem(item_text)
        
        # 设置用户数据，存储方向和帧索引
        list_item.setData(Qt.UserRole, {"direction": direction, "frame": frame_index})
        
        # 添加到列表
        self.history_list.addItem(list_item)
        self.history_list.scrollToBottom()
        
        # 保存引用
        self.history_items[key] = list_item

    def _on_history_item_clicked(self, item):
        """当点击历史记录项时跳转到对应帧和方向"""
        data = item.data(Qt.UserRole)
        if not data:
            return
            
        direction = data["direction"]
        frame_index = data["frame"]
        
        # 保存当前提示（如果需要）
        if self._has_prompts(self.current_frame):
            self._save_current_frame_prompts_if_needed(self.current_frame)
        
        # 更新当前方向
        self.current_direction = direction
        
        # 跳转到对应的Z层
        self.viewer.dims.current_step = (frame_index, 0, 0)
        self.current_frame = frame_index

        # 更新提示显示
        self._update_prompts_display()
        
        show_info(f"Jumped to {direction.upper()} direction, frame {frame_index}")

    def _clear_history(self):
        """清除历史记录"""
        self.prompted_frames.clear()
        self.history_items.clear()
        self.history_list.clear()


    def _clear_history(self):
        """清除历史记录"""
        self.prompts_history = []
        self.history_list.clear()

    def _normalize_box(self, start_pos, end_pos):
        """将任意两个点规范化为矩形框的左上角和右下角坐标"""
        # 确保左上角在左下角之前，左边界在右边界之前
        y1, x1 = start_pos
        y2, x2 = end_pos

        # 确保 x1 <= x2, y1 <= y2
        x1, x2 = sorted([x1, x2])
        y1, y2 = sorted([y1, y2])

        return (x1, y1, x2, y2)

    def _get_or_create_layer(self, name, layer_type):
        """获取或创建指定类型的提示图层"""
        if name in self.viewer.layers:
            return self.viewer.layers[name]
        return self.viewer.add_layer(layer_type(name=name, ndim=3))

    def _create_prompt_layers(self):
        """创建提示图层"""
        # 点提示图层
        if "points_prompts" in self.viewer.layers:
            self.points_layer = self.viewer.layers["points_prompts"]
        else:
            self.points_layer = self.viewer.add_points(
                name="points_prompts", ndim=2, face_color="red", size=5
            )
        self.points_layer.z_index = 0
        # 框提示图层
        if "box_prompts" in self.viewer.layers:
            self.boxes_layer = self.viewer.layers["box_prompts"]
        else:
            self.boxes_layer = self.viewer.add_shapes(
                name="box_prompts", ndim=2, edge_color="blue", face_color="transparent"
            )
            # 获取形状控件
            shapes_controls = self.viewer.window.qt_viewer.controls.widgets[
                self.boxes_layer
            ]

            # 查找按钮组
            button_group = None
            for child in shapes_controls.findChildren(QButtonGroup):
                button_group = child
                break

            if button_group:
                # 获取所有按钮
                all_buttons = button_group.buttons()

                allowed_buttons = {"select", "rectangle"}

                # 隐藏不需要的按钮
                for id in range(0, 8):
                    button_id = 12 - id
                    if button_id > 0:
                        button = all_buttons[button_id]
                        button_group.removeButton(all_buttons[button_id])
                        button.setParent(None)
                        # button.setVisible(False)
                        # button.setEnabled(False)  # 双重保险禁用按钮
            button_group.removeButton(all_buttons[0])
            test_all_buttons = button_group.buttons()
            # 强制设置为矩形模式
            self.boxes_layer.mode = "add_rectangle"
        self.boxes_layer.z_index = 1

        for direction in ["x", "y", "z"]:
            layer_name = f"mask_prompts_{direction}"

            # 创建初始掩码数据 (1x1x1)
            mask_data = np.zeros((1, 1, 1), dtype=np.uint8)

            if layer_name in self.viewer.layers:
                self.masks_layers[direction] = self.viewer.layers[layer_name]
            else:
                self.masks_layers[direction] = self.viewer.add_labels(
                    mask_data,
                    name=layer_name,
                    opacity=0.7,
                    blending='translucent',  # 确保透明混合
                    visible=(direction == self.current_direction),  # 只显示当前方向
                )
                # 设置图层顺序
                self.masks_layers[direction].z_index = 1

    def _update_mask_layer_shape(self, shape: tuple):
        """更新掩码图层形状以匹配体积数据"""
        if len(shape) != 3:
            return

        # 更新每个方向的掩码图层形状
        for direction in ["x", "y", "z"]:
            if self.masks_layers[direction]:
                # 创建全零的掩码数据
                mask_data = np.zeros(shape, dtype=np.uint8)

                # 恢复之前保存的掩码提示
                if direction in self.prompts:
                    for frame_index, mask in self.prompts[direction]["masks"].items():

                        # 检查帧索引是否在有效范围内
                        if direction == "z" and frame_index < shape[0]:
                            mask_data[frame_index, :, :] = mask
                        elif direction == "y" and frame_index < shape[1]:
                            mask_data[:, frame_index, :] = mask
                        elif direction == "x" and frame_index < shape[2]:
                            mask_data[:, :, frame_index] = mask

                # 更新图层数据
                self.masks_layers[direction].data = mask_data

    def _generate_condition_frames(self):
        """生成条件帧并显示"""
        if not self.predictor:
            show_info("Please initialize predictor first")
            return

        image_layer = self._get_image_layer()
        if not image_layer:
            show_info("Please load volume data first")
            return

        # 保存当前帧的提示
        self._save_current_frame_prompts_if_needed(self.current_frame)

        # 创建Prediction实例并生成条件帧
        my_prediction = Prediction(
            adapter=self.predictor,
            volume=image_layer.data,
            prompts=self.prompts,
            current_obj_id=self.current_obj_id,
        )

        condition_frames = my_prediction.generate_condition_frames()
        show_info(f"Generated condition frames for {len(condition_frames)} directions")

        # 存储预测器实例以备后续传播使用
        self.current_prediction = my_prediction
        # except Exception as e:
        #     show_info(f"Failed to generate condition frames: {str(e)}")

    def _show_condition_frames(self, condition_frames):
        """在napari中显示条件帧（仅显示当前方向）"""
        # 只显示当前方向的条件帧
        direction = self.current_direction
        if direction not in condition_frames:
            return

        # 创建三维数组存储条件帧
        shape = self.viewer.layers["3d_volume"].data.shape
        condition_volume = np.zeros(shape, dtype=np.uint8)

        # 将条件帧放入对应位置
        for frame_index, mask in condition_frames[direction].items():
            condition_volume[frame_index] = mask

        # 添加到napari
        layer_name = f"condition_frames_{direction}"
        if layer_name in self.viewer.layers:
            self.viewer.layers[layer_name].data = condition_volume
        else:
            self.viewer.add_labels(condition_volume, name=layer_name, opacity=0.7)

    def _run_propagation(self):
        """执行三维传播预测"""
        # 检查条件帧是否全为0
        zero_frames = []
        valid_frames = []
        condition_frames = self.current_prediction.condition_frames

        for direction, frame in condition_frames.items():
            # 检查帧是否为全零
            is_zero = True

            if np.any(frame != 0):  # 存在非零值
                is_zero = False

            if is_zero:
                zero_frames.append(direction)
            else:
                valid_frames.append(direction)

        # 显示详细结果
        if zero_frames:
            show_info(
                f"Warning: Zero condition frames in directions: {', '.join(zero_frames)}"
            )

        if valid_frames:
            show_info(
                f"Valid condition frames in directions: {', '.join(valid_frames)}"
            )
        else:
            show_info(
                "Warning: All condition frames are zero (invalid condition frames)"
            )

        if not hasattr(self, "current_prediction"):
            show_info("Please generate condition frames first")
            return

        # 检查是否有条件帧
        if not self.current_prediction.condition_frames:
            show_info("No condition frames available for propagation")
            return

        # 确保至少有一个方向有条件帧
        valid_directions = [
            d
            for d in self.current_prediction.condition_frames
            if self.current_prediction.condition_frames[d]
        ]
        if not valid_directions:
            show_info("No valid condition frames for propagation")
            return

        # 使用条件帧执行传播预测
        # result = self.current_prediction.run_propagation()
        result = self.current_prediction.run_propagation().float().cpu().numpy()
        # 检查结果是否全为0
        # if np.all(result == 0):
        #     show_info("警告: 传播预测结果全为零！")
        # else:
        #     # 显示结果
        #     self._show_result(result)
        #     show_info("传播完成")
        # 显示结果
        self._show_result(result)
        show_info("Propagation completed")
        # except Exception as e:
        #     show_info(f"Propagation failed: {str(e)}")

    def _show_result(self, result):
        """显示三维预测结果"""
        # try:
        # 转换为整数类型
        # 打印调试信息
        print(f"结果形状: {result.shape}")
        print(f"最小值: {result.min()}, 最大值: {result.max()}")
        print(f"非零像素数: {np.count_nonzero(result)}")

        # 应用阈值并归一化
        threshold = 0  # 根据您的模型调整这个阈值
        result = (result > threshold).astype(np.uint8) * 255

        print(f"转化后最小值: {result.min()}, 转化后最大值: {result.max()}")
        print(f"转化后非零像素数: {np.count_nonzero(result)}")

        # 创建或更新分割图层
        if "segmentation" in self.viewer.layers:
            seg_layer = self.viewer.layers["segmentation"]
            seg_layer.data = result
            seg_layer.refresh()  # 强制刷新显示
        else:
            seg_layer = self.viewer.add_labels(
                result, name="segmentation", opacity=0.7, blending="translucent"
            )
            # 设置图层顺序，确保它在图像之上

        # 确保图层可见
        seg_layer.visible = True
        show_info("Prediction completed")
        # except Exception as e:
        #     show_info(f"Result display failed: {str(e)}")

    def _save_result(self, path=None):
        if "segmentation" not in self.viewer.layers:
            show_info("No prediction result to save")
            return

        path_filter = QFileDialog.getSaveFileName(
            self,
            "Save Segmentation Result",
            "",
            "Zstandard compressed files (*.zst);;" "NumPy compressed files (*.npz);;",
        )
        # 根据选择的文件过滤器添加正确的扩展名

        if path_filter[1] == "Zstandard compressed files (*.zst)" and not path_filter[
            0
        ].endswith(".zst"):
            path = path_filter[0] + ".zst"
        elif path_filter[1] == "NumPy compressed files (*.npz)" and not path_filter[
            0
        ].endswith(".npz"):
            path = path_filter[0] + ".npz"

        if path:
            try:
                seg_layer = self.viewer.layers["segmentation"]
                seg_volume_obj = Volume()
                seg_volume_obj.set_volume(seg_layer.data)
                seg_volume_obj.save_volume(path)
                show_info(f"Result saved to {path}")
            except Exception as e:
                show_info(f"Saving failed: {str(e)}")

    def _get_image_layer(self) -> Optional[Image]:
        for layer in self.viewer.layers:
            if isinstance(layer, Image) and layer.ndim == 3:
                return layer
        return None
