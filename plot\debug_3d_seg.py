# debug_3d_seg.py
from napari import Viewer, run
from dataprocess.volume import Volume  # 根据实际文件位置调整导入路径
import numpy as np
from qtpy.QtCore import QThread, Signal, Qt
from qtpy.QtCore import QTimer
import time

# 创建 Viewer 实例
viewer = Viewer()

# 添加你的插件部件
dock_widget, seg3d_widget = viewer.window.add_plugin_dock_widget(
    plugin_name="3d-seg",  # 插件名称（与 contributions.yaml 中的 name 字段一致）
    widget_name="segmentation 3d",  # 部件的显示名称（display_name）
)

# ================== 加载真实数据 ==================
volume_path = "/data2/wyx/3d_seg/finetune_ready/em/val/em_s0_222.zst"  # 替换为真实路径


def add_sample_masks(direction):
    # 根据方向设置维度顺序
    if direction == "x":
        # x轴切片：视图平面为yz平面
        viewer.dims.order = (2, 1, 0)
        print("切片方向设置为 x 轴 (视图平面: yz)")
    elif direction == "y":
        # y轴切片：视图平面为xz平面
        viewer.dims.order = (1, 0, 2)
        print("切片方向设置为 y 轴 (视图平面: xz)")
    elif direction == "z":  # 'z'
        # z轴切片：视图平面为xy平面（默认）
        viewer.dims.order = (0, 1, 2)
        print("切片方向设置为 z 轴 (视图平面: xy)")
    """添加示例掩码"""
    # 在第50层添加矩形掩码
    mask_path = (
        "/data2/wyx/3d_seg/finetune_ready/seg/val/mito_seg_222.zst"  # 替换为真实路径
    )
    volume = Volume(mask_path)
    volume.load()
    volume.volume_to_binary_8bit()
    # volume.plot_slice()
    volume.volume_to_frames(
        img_start=(0, 0),
        img_size=(1024, 1024),
        frame_start=0,
        frame_end=10,
        direction=direction,
        use_pil=True,
    )
    volume.frames
    z_layer = 0
    sample_mask = volume.frames[0]

    mask_layer = seg3d_widget.masks_layers[direction]

    # 获取当前体积数据的形状 (z, y, x)
    volume_shape = mask_layer.data.shape

    # 创建新的掩码数据副本
    new_mask_data = mask_layer.data.copy()

    # 根据方向确定如何插入掩码
    if direction == "z":
        # z方向：掩码放在z=0切片
        if volume_shape[0] > 0:
            new_mask_data[0] = sample_mask
    elif direction == "y":
        # y方向：掩码放在y=0切片
        if volume_shape[1] > 0:
            new_mask_data[:, 0, :] = sample_mask
    elif direction == "x":
        # x方向：掩码放在x=0切片
        if volume_shape[2] > 0:
            new_mask_data[:, :, 0] = np.array(sample_mask).T

    # 更新掩码图层
    mask_layer.data = new_mask_data

    # 检查添加后的掩码是否全为0
    if direction == "z" and np.all(mask_layer.data[0] == 0):
        print(f"第0层的掩码全为0")
    elif direction == "y" and np.all(mask_layer.data[:, 0, :] == 0):
        print(f"y=0切片的掩码全为0")
    elif direction == "x" and np.all(mask_layer.data[:, :, 0] == 0):
        print(f"x=0切片的掩码全为0")


viewer.dims.current_step = (0, 0, 0)  # 设置到第一层


# 添加示例点提示和框提示
def add_sample_prompts():
    """添加示例点提示和框提示"""
    # 获取点图层和框图层
    points_layer = viewer.layers["points_prompts"]
    boxes_layer = viewer.layers["box_prompts"]

    # 正点（前景点）
    points_layer.add([578, 70])

    # 在第50层添加框提示
    box = np.array(
        [
            [876, 744],  # 左上角
            [1000, 887],  # 右下角
        ]
    )
    boxes_layer.add_rectangles(box)


# add_sample_prompts()
seg3d_widget._load_volume(volume_path)
add_sample_prompts()
#viewer.dims.current_step = (1, 0, 0)  # 设置到第一层
add_sample_masks("x")
add_sample_masks("y")
# add_sample_masks('z')
# add_sample_masks()
# viewer.dims.current_step = (3, 0, 0)  # 设置到第一层

# 运行预测

seg3d_widget._generate_condition_frames()
#seg3d_widget._run_propagation()


# 使用定时器延迟执行预测
def delayed_prediction():
    # # 添加示例提示
    # add_sample_prompts()
    # seg3d_widget._load_volume(volume_path)
    # # 运行预测
    seg3d_widget._run_prediction()


# 设置延迟（1秒后执行）
# QTimer.singleShot(1000, delayed_prediction)

# 启动napari
run()
