import os
import numpy as np
import json
import hydra
import time
import torch
from joblib import Parallel, delayed
import math
import gc
import logging
from dataprocess.volume import Volume
from tqdm import tqdm
from scipy.optimize import linear_sum_assignment
from utils.misc import convert_to_json_serializable

# Configure logger
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)


def compute_metrics(preds, labels):
    preds = preds > 0
    labels = labels > 0

    intersection = np.logical_and(preds, labels)
    union = np.logical_or(preds, labels)

    sum_intersection = np.sum(intersection)
    sum_union = np.sum(union)
    sum_preds = np.sum(preds)
    sum_labels = np.sum(labels)

    dice = (
        2 * sum_intersection / (sum_preds + sum_labels)
        if sum_preds + sum_labels > 0
        else 0
    )
    iou = sum_intersection / sum_union if sum_union > 0 else 0
    precision = sum_intersection / sum_preds if sum_preds > 0 else 0
    recall = sum_intersection / sum_labels if sum_labels > 0 else 0

    # Calculate accuracy as tp/(tp+fp+fn)
    tp = sum_intersection
    fp = sum_preds - sum_intersection
    fn = sum_labels - sum_intersection
    accuracy = tp / (tp + fp + fn) if (tp + fp + fn) > 0 else 0

    return {
        "dice": dice,
        "iou": iou,
        "precision": precision,
        "recall": recall,
        "accuracy": accuracy,
    }


def compute_instance_pair_iou(task_data):
    """
    并行计算实例对的IoU

    Args:
        task_data: 包含实例对信息的元组 (pred_id, gt_id, i, j, pred_bbox, gt_bbox, pred_positions, gt_positions)

    Returns:
        tuple: (i, j, iou) 如果边界框重叠，否则返回 None
    """
    pred_id, gt_id, i, j, pred_bbox, gt_bbox, pred_positions, gt_positions = task_data

    # 检查边界框是否重叠
    if (
        pred_bbox[0] > gt_bbox[3]
        or pred_bbox[3] < gt_bbox[0]
        or pred_bbox[1] > gt_bbox[4]
        or pred_bbox[4] < gt_bbox[1]
        or pred_bbox[2] > gt_bbox[5]
        or pred_bbox[5] < gt_bbox[2]
    ):
        # 边界框不重叠，IoU为0
        return None

    # 边界框重叠，计算精确的IoU
    pred_pos = pred_positions[pred_id]
    gt_pos = gt_positions[gt_id]

    pred_voxels = set(zip(*pred_pos))  # 转换为集合以加速查找
    gt_voxels = set(zip(*gt_pos))  # 转换为集合以加速查找

    # 计算交集和并集的大小
    intersection = len(pred_voxels.intersection(gt_voxels))
    union = len(pred_voxels) + len(gt_voxels) - intersection

    iou = intersection / union if union > 0 else 0
    return (i, j, iou)


def compute_instance_metrics(pred_volume, gt_volume, iou_threshold=0.5, n_jobs=-1):
    """
    计算实例分割的指标，包括mAP

    Args:
        pred_volume: 预测的实例分割结果，不同整数代表不同实例
        gt_volume: 真实的实例分割结果，不同整数代表不同实例
        iou_threshold: IoU阈值，大于该阈值的匹配被视为正确匹配
        n_jobs: 并行作业数，-1表示使用所有可用的CPU核心

    Returns:
        dict: 包含mAP和其他指标的字典
    """
    start_time = time.time()
    # 获取所有实例ID（排除背景0）
    pred_instance_ids = np.unique(pred_volume)
    pred_instance_ids = pred_instance_ids[pred_instance_ids > 0]

    gt_instance_ids = np.unique(gt_volume)
    gt_instance_ids = gt_instance_ids[gt_instance_ids > 0]

    # 如果没有预测实例或真实实例，返回特殊值
    if len(pred_instance_ids) == 0 and len(gt_instance_ids) == 0:
        return {
            "mAP": 1.0,
            "precision": 1.0,
            "recall": 1.0,
            "accuracy": 1.0,
            "matched_instances": 0,
            "total_pred_instances": 0,
            "total_gt_instances": 0,
            "AP_values": {},
        }
    if len(pred_instance_ids) == 0:
        return {
            "mAP": 0.0,
            "precision": 0.0,
            "recall": 0.0,
            "accuracy": 0.0,
            "matched_instances": 0,
            "total_pred_instances": 0,
            "total_gt_instances": int(len(gt_instance_ids)),
            "AP_values": {},
        }
    if len(gt_instance_ids) == 0:
        return {
            "mAP": 0.0,
            "precision": 0.0,
            "recall": 0.0,
            "accuracy": 0.0,
            "matched_instances": 0,
            "total_pred_instances": int(len(pred_instance_ids)),
            "total_gt_instances": 0,
            "AP_values": {},
        }

    # 优化1: 预先计算每个实例的边界框和体素位置
    pred_bboxes = {}
    gt_bboxes = {}
    pred_positions = {}
    gt_positions = {}

    # 计算预测实例的边界框和位置
    for pred_id in tqdm(
        pred_instance_ids, desc="Computing predicted instance bounding boxes"
    ):
        # 获取实例的位置索引
        pred_pos = np.where(pred_volume == pred_id)
        pred_positions[pred_id] = pred_pos

        # 计算边界框 (min_z, min_y, min_x, max_z, max_y, max_x)
        if len(pred_pos[0]) > 0:  # 确保实例不为空
            min_z, max_z = np.min(pred_pos[0]), np.max(pred_pos[0])
            min_y, max_y = np.min(pred_pos[1]), np.max(pred_pos[1])
            min_x, max_x = np.min(pred_pos[2]), np.max(pred_pos[2])
            pred_bboxes[pred_id] = (min_z, min_y, min_x, max_z, max_y, max_x)

    # 计算真实实例的边界框和位置
    for gt_id in tqdm(
        gt_instance_ids, desc="Computing ground truth instance bounding boxes"
    ):
        gt_pos = np.where(gt_volume == gt_id)
        gt_positions[gt_id] = gt_pos

        if len(gt_pos[0]) > 0:  # 确保实例不为空
            min_z, max_z = np.min(gt_pos[0]), np.max(gt_pos[0])
            min_y, max_y = np.min(gt_pos[1]), np.max(gt_pos[1])
            min_x, max_x = np.min(gt_pos[2]), np.max(gt_pos[2])
            gt_bboxes[gt_id] = (min_z, min_y, min_x, max_z, max_y, max_x)

    # 计算IoU矩阵
    iou_matrix = np.zeros((len(pred_instance_ids), len(gt_instance_ids)))

    # 输出实例总数信息
    logger.info(
        f"Computing IoU for {len(pred_instance_ids)} predicted instances and {len(gt_instance_ids)} ground truth instances"
    )

    # 准备并行计算的任务
    tasks = []
    for i, pred_id in enumerate(pred_instance_ids):
        if pred_id not in pred_bboxes:
            continue  # 跳过空实例

        pred_bbox = pred_bboxes[pred_id]

        for j, gt_id in enumerate(gt_instance_ids):
            if gt_id not in gt_bboxes:
                continue  # 跳过空实例

            gt_bbox = gt_bboxes[gt_id]

            # 添加到并行任务列表
            tasks.append(
                (pred_id, gt_id, i, j, pred_bbox, gt_bbox, pred_positions, gt_positions)
            )

    logger.info(f"Found {len(tasks)} potential instance pairs to evaluate")
    logger.info(
        f"Using {n_jobs if n_jobs > 0 else 'all available'} CPU cores for parallel processing"
    )

    # 并行计算IoU
    results = Parallel(n_jobs=n_jobs)(
        delayed(compute_instance_pair_iou)(task)
        for task in tqdm(tasks, desc="Computing IoU in parallel")
    )

    # 将结果填入IoU矩阵
    match_count = 0
    for result in results:
        if result is not None:
            i, j, iou = result
            iou_matrix[i, j] = iou
            match_count += 1

    logger.info(
        f"Computed IoU for {match_count} instance pairs with overlapping bounding boxes"
    )

    # 计算处理时间
    bbox_and_iou_time = time.time() - start_time
    logger.info(
        f"Bounding box and IoU computation took {bbox_and_iou_time:.2f} seconds"
    )

    # 使用匈牙利算法找到最佳匹配
    row_ind, col_ind = linear_sum_assignment(-iou_matrix)  # 最大化IoU

    # 计算匹配的IoU值
    matched_ious = iou_matrix[row_ind, col_ind]

    # 计算TP (True Positives)
    true_positives = (matched_ious >= iou_threshold).sum()
    # 计算FP (False Positives) 和 FN (False Negatives)
    false_positives = len(pred_instance_ids) - true_positives
    false_negatives = len(gt_instance_ids) - true_positives

    # 计算精确度和召回率
    precision = (
        true_positives / len(pred_instance_ids) if len(pred_instance_ids) > 0 else 0
    )
    recall = true_positives / len(gt_instance_ids) if len(gt_instance_ids) > 0 else 0

    # 计算准确率 (Accuracy) = TP / (TP + FP + FN)
    accuracy = (
        true_positives / (true_positives + false_positives + false_negatives)
        if (true_positives + false_positives + false_negatives) > 0
        else 0
    )

    # 计算不同IoU阈值下的AP
    ap_thresholds = np.arange(0.5, 1.0, 0.05)  # [0.5, 0.55, ..., 0.95]
    ap_values = []

    for threshold in ap_thresholds:
        tp = (matched_ious >= threshold).sum()
        p = tp / len(pred_instance_ids) if len(pred_instance_ids) > 0 else 0
        r = tp / len(gt_instance_ids) if len(gt_instance_ids) > 0 else 0
        f1 = 2 * p * r / (p + r) if (p + r) > 0 else 0
        ap_values.append(f1)  # 使用F1作为AP的近似

    # 计算mAP
    mAP = float(np.mean(ap_values)) if ap_values else 0.0

    # 返回指标，确保所有值都是可序列化的
    return {
        "mAP": float(mAP),
        "precision": float(precision),
        "recall": float(recall),
        "accuracy": float(accuracy),
        "matched_instances": int(true_positives),
        "total_pred_instances": int(len(pred_instance_ids)),
        "total_gt_instances": int(len(gt_instance_ids)),
        "AP_values": {
            f"AP{int(t*100)}": float(v) for t, v in zip(ap_thresholds, ap_values)
        },
    }


def get_chunk_coordinates(volume_shape, num_chunks=8):
    """
    获取分块的坐标信息，不实际加载块数据

    Args:
        volume_shape: 体积的形状
        num_chunks: 分割成的块数，默认为8（8=2x2x2）

    Returns:
        list: 包含分割坐标信息的列表
    """
    # 确定每个维度的分割数量
    chunks_per_dim = int(round(num_chunks ** (1 / 3)))

    z, y, x = volume_shape
    z_step = math.ceil(z / chunks_per_dim)
    y_step = math.ceil(y / chunks_per_dim)
    x_step = math.ceil(x / chunks_per_dim)

    chunk_coords = []

    for i in range(chunks_per_dim):
        z_start = i * z_step
        z_end = min((i + 1) * z_step, z)

        for j in range(chunks_per_dim):
            y_start = j * y_step
            y_end = min((j + 1) * y_step, y)

            for k in range(chunks_per_dim):
                x_start = k * x_step
                x_end = min((k + 1) * x_step, x)

                # 存储坐标信息
                chunk_coords.append((z_start, z_end, y_start, y_end, x_start, x_end))

    return chunk_coords


def extract_chunk(volume, coords):
    """
    从体积中提取指定坐标的块

    Args:
        volume: 输入体积
        coords: 坐标信息 (z_start, z_end, y_start, y_end, x_start, x_end)

    Returns:
        ndarray: 提取的块
    """
    z_start, z_end, y_start, y_end, x_start, x_end = coords
    return volume[z_start:z_end, y_start:y_end, x_start:x_end]


def process_chunk(chunk_data, iou_threshold=0.5, use_gpu=False):
    """
    处理单个块的实例分割评估

    Args:
        chunk_data: 包含预测和真实块的字典
        iou_threshold: IoU阈值
        use_gpu: 是否使用GPU加速

    Returns:
        dict: 评估结果和实例信息
    """
    pred_chunk = chunk_data["pred_chunk"]["chunk"]
    gt_chunk = chunk_data["gt_chunk"]["chunk"]
    coords = chunk_data["pred_chunk"]["coords"]

    # 计算块的实例分割指标
    metrics = compute_instance_metrics(pred_chunk, gt_chunk, iou_threshold)

    # 获取块中的实例信息
    pred_instance_ids = np.unique(pred_chunk)
    pred_instance_ids = pred_instance_ids[pred_instance_ids > 0]

    gt_instance_ids = np.unique(gt_chunk)
    gt_instance_ids = gt_instance_ids[gt_instance_ids > 0]

    # 返回结果和实例信息
    return {
        "metrics": metrics,
        "coords": coords,
        "pred_instances": pred_instance_ids.tolist(),
        "gt_instances": gt_instance_ids.tolist(),
    }


def merge_chunk_results(chunk_results):
    """
    合并多个块的评估结果

    Args:
        chunk_results: 各个块的评估结果列表

    Returns:
        dict: 合并后的评估指标
    """
    # 初始化结果
    total_pred_instances = 0
    total_gt_instances = 0
    total_matched_instances = 0
    all_ap_values = {}

    # 统计实例数量
    unique_pred_instances = set()
    unique_gt_instances = set()

    # 合并各块的结果
    for result in chunk_results:
        metrics = result["metrics"]

        # 确保所有必要的键都存在
        if "total_pred_instances" in metrics:
            total_pred_instances += metrics["total_pred_instances"]
        if "total_gt_instances" in metrics:
            total_gt_instances += metrics["total_gt_instances"]
        if "matched_instances" in metrics:
            total_matched_instances += metrics["matched_instances"]

        # 收集不同AP阈值的结果
        if "AP_values" in metrics:
            for ap_key, ap_value in metrics["AP_values"].items():
                if ap_key not in all_ap_values:
                    all_ap_values[ap_key] = []
                all_ap_values[ap_key].append(ap_value)

        # 收集实例信息
        for pred_id in result["pred_instances"]:
            unique_pred_instances.add(pred_id)
        for gt_id in result["gt_instances"]:
            unique_gt_instances.add(gt_id)

    # 计算平均AP值
    avg_ap_values = {k: float(np.mean(v)) for k, v in all_ap_values.items()}
    mAP = float(np.mean(list(avg_ap_values.values()))) if avg_ap_values else 0.0

    # 计算精确度和召回率
    # 注意：这里的计算可能会有误差，因为边界实例可能被重复计算
    precision = (
        float(total_matched_instances / total_pred_instances)
        if total_pred_instances > 0
        else 0.0
    )
    recall = (
        float(total_matched_instances / total_gt_instances)
        if total_gt_instances > 0
        else 0.0
    )

    # 计算准确率 (Accuracy) = TP / (TP + FP + FN)
    # TP = total_matched_instances
    # FP = total_pred_instances - total_matched_instances
    # FN = total_gt_instances - total_matched_instances
    accuracy = (
        float(
            total_matched_instances
            / (total_pred_instances + total_gt_instances - total_matched_instances)
        )
        if (total_pred_instances + total_gt_instances - total_matched_instances) > 0
        else 0.0
    )

    # 返回合并结果，确保所有值都是可序列化的
    return {
        "mAP": float(mAP),
        "precision": float(precision),
        "recall": float(recall),
        "accuracy": float(accuracy),
        "matched_instances": int(total_matched_instances),
        "total_pred_instances": int(len(unique_pred_instances)),  # 使用唯一实例数
        "total_gt_instances": int(len(unique_gt_instances)),  # 使用唯一实例数
        "AP_values": avg_ap_values,  # 已经在前面转换为float
        "note": "该结果是通过将体积分割成多个小块并行计算得到的，边界实例可能有误差",
    }


def evaluate_volume(
    pred_path,
    true_path,
    instance_mode=False,
    iou_threshold=0.5,
    use_chunks=False,
    num_chunks=8,
    n_jobs=-1,
    use_gpu=False,
):
    """
    评估体积分割结果

    Args:
        pred_path: 预测结果的路径
        true_path: 真实标签的路径
        instance_mode: 是否为实例分割模式
        iou_threshold: 实例分割模式下的IoU阈值
        use_chunks: 是否将体积分割成小块并行处理
        num_chunks: 分割成的块数，默认为8（8=2x2x2）
        n_jobs: 并行作业数，-1表示使用所有可用的CPU核心
        use_gpu: 是否使用GPU加速（如果可用）

    Returns:
        dict: 评估指标
    """
    start_time = time.time()

    pred_mask = Volume(pred_path)
    pred_mask.load()
    true_mask = Volume(true_path)
    true_mask.load()

    # 如果不是实例分割模式，则转换为二值图像
    if not instance_mode:
        # convert to binary if not already
        true_mask.volume_to_binary_8bit()

    # See if true mask needs to scale
    if pred_mask.volume.shape != true_mask.volume.shape:
        scale = [
            pred_mask.volume.shape[i] / true_mask.volume.shape[i] for i in range(3)
        ]
        true_mask.scale_volume(scale)

    if instance_mode:
        if (
            use_chunks and pred_mask.volume.size > 100_000_000
        ):  # 如果体积较大，才使用分块
            logger.info(f"Volume size: {pred_mask.volume.shape}")
            logger.info(
                f"Splitting volume into {num_chunks} chunks for parallel processing..."
            )

            # 获取分块坐标信息，不实际加载块数据
            chunk_coords = get_chunk_coordinates(pred_mask.volume.shape, num_chunks)
            logger.info(f"Total chunks to process: {len(chunk_coords)}")

            # 初始化结果列表
            chunk_results = []

            # 逐块处理，每次只保留当前块在内存中
            logger.info("Processing chunks one by one to minimize memory usage...")
            for chunk_idx, coords in enumerate(chunk_coords):
                logger.info(f"Processing chunk {chunk_idx+1}/{len(chunk_coords)}")

                # 提取当前块
                pred_chunk = extract_chunk(pred_mask.volume, coords)
                gt_chunk = extract_chunk(true_mask.volume, coords)

                # 构建块数据
                chunk_data = {
                    "pred_chunk": {"chunk": pred_chunk, "coords": coords},
                    "gt_chunk": {"chunk": gt_chunk, "coords": coords},
                }

                # 处理当前块
                result = process_chunk(chunk_data, iou_threshold, use_gpu)
                chunk_results.append(result)

                # 清理内存
                del pred_chunk
                del gt_chunk
                gc.collect()

            # 释放原始体积数据
            del pred_mask.volume
            del true_mask.volume
            gc.collect()

            # 合并结果
            logger.info("Merging results from all chunks...")
            result = merge_chunk_results(chunk_results)

            # 添加处理时间信息
            processing_time = time.time() - start_time
            result["processing_time"] = processing_time
            logger.info(f"Processing completed in {processing_time:.2f} seconds")

            return result
        else:
            # 使用原始方法处理，但使用并行计算
            result = compute_instance_metrics(
                pred_mask.volume, true_mask.volume, iou_threshold, n_jobs=n_jobs
            )
            processing_time = time.time() - start_time
            result["processing_time"] = processing_time
            return result
    else:
        result = compute_metrics(pred_mask.volume, true_mask.volume)
        processing_time = time.time() - start_time
        result["processing_time"] = processing_time
        return result


def evaluate_instance_segmentation(
    pred_dir,
    true_dir,
    iou_threshold=0.5,
    use_chunks=False,
    num_chunks=8,
    n_jobs=-1,
    use_gpu=False,
):
    """
    评估实例分割结果

    Args:
        pred_dir: 预测结果目录
        true_dir: 真实标签目录
        iou_threshold: IoU阈值
        use_chunks: 是否将体积分割成小块并行处理
        num_chunks: 分割成的块数，默认为8（8=2x2x2）
        n_jobs: 并行作业数，-1表示使用所有可用的CPU核心
        use_gpu: 是否使用GPU加速（如果可用）

    Returns:
        dict: 评估指标
    """
    metrics_all = {}

    # 获取所有需要评估的文件
    files_to_evaluate = []
    for file in os.listdir(pred_dir):
        if file.endswith(".zst") or file.endswith(".npz"):
            # 从文件名中提取ID
            idx_with_surfix = file.split("_")[-2:]
            idx = idx_with_surfix[0]
            surfix = idx_with_surfix[1].split(".")[0]

            # 构建真实标签路径
            true_path = os.path.join(true_dir, f"mito_seg_{idx}.zst")
            if not os.path.exists(true_path):
                true_path = os.path.join(true_dir, f"mito_seg_{idx}.npz")

            if not os.path.exists(true_path):
                print(f"Warning: Ground truth not found for {file}")
                continue

            files_to_evaluate.append(
                (file, idx, surfix, os.path.join(pred_dir, file), true_path)
            )

    logger.info(f"Found {len(files_to_evaluate)} files to evaluate")

    # 处理所有文件
    for file, idx, surfix, pred_path, true_path in tqdm(
        files_to_evaluate, desc="Evaluating files"
    ):
        logger.info(f"Evaluating {file}...")

        # 评估实例分割结果，使用分块和并行处理
        metrics = evaluate_volume(
            pred_path,
            true_path,
            instance_mode=True,
            iou_threshold=iou_threshold,
            use_chunks=use_chunks,
            num_chunks=num_chunks,
            n_jobs=n_jobs,
            use_gpu=use_gpu,
        )

        # 输出实例数量信息
        logger.info(f"  Predicted instances: {metrics['total_pred_instances']}")
        logger.info(f"  Ground truth instances: {metrics['total_gt_instances']}")
        logger.info(f"  Matched instances: {metrics['matched_instances']}")
        logger.info(
            f"  mAP: {metrics['mAP']:.4f}, Precision: {metrics['precision']:.4f}, Recall: {metrics['recall']:.4f}, Accuracy: {metrics['accuracy']:.4f}"
        )
        if "processing_time" in metrics:
            logger.info(f"  Processing time: {metrics['processing_time']:.2f} seconds")

        if idx not in metrics_all:
            metrics_all[idx] = {}
        metrics_all[idx][surfix] = metrics

    return metrics_all


@hydra.main(config_path="../config", config_name="config", version_base=None)
def main(cfg):
    # 默认路径
    pred_dir = cfg.get("pred_dir", os.path.join(cfg.output_root, "gaussian_blur"))
    true_dir = cfg.get("true_dir", os.path.join(cfg.datasets_root, "mito_seg/val"))

    # 评估参数
    instance_mode = cfg.get("instance_mode", False)
    iou_threshold = cfg.get("iou_threshold", 0.5)

    # 并行处理参数
    use_chunks = cfg.get("use_chunks", False)
    num_chunks = cfg.get("num_chunks", 8)
    n_jobs = cfg.get("n_jobs", -1)
    use_gpu = cfg.get("use_gpu", False)

    logger.info(f"Prediction directory: {pred_dir}")
    logger.info(f"Ground truth directory: {true_dir}")

    if instance_mode:
        logger.info(
            f"Evaluating instance segmentation with IoU threshold: {iou_threshold}"
        )

        if use_chunks:
            logger.info(f"Using chunked processing with {num_chunks} chunks")
            logger.info(
                f"Parallel processing with {n_jobs if n_jobs > 0 else 'all available'} CPU cores"
            )
            if use_gpu and torch.cuda.is_available():
                logger.info(
                    f"GPU acceleration enabled using {torch.cuda.get_device_name(0)}"
                )
            elif use_gpu:
                logger.info(
                    "GPU acceleration requested but no GPU available, using CPU instead"
                )
                use_gpu = False

        # 清理内存
        gc.collect()

        metrics_all = evaluate_instance_segmentation(
            pred_dir,
            true_dir,
            iou_threshold=iou_threshold,
            use_chunks=use_chunks,
            num_chunks=num_chunks,
            n_jobs=n_jobs,
            use_gpu=use_gpu,
        )
        output_file = os.path.join(pred_dir, "instance_metrics.json")
    else:
        logger.info("Evaluating semantic segmentation")
        metrics_all = {}

        # 获取所有需要评佐的文件
        files_to_evaluate = []
        for file in os.listdir(pred_dir):
            if file.endswith(".zst") or file.endswith(".npz"):
                idx_with_surfix = file.split("_")[-2:]
                idx = idx_with_surfix[0]
                surfix = idx_with_surfix[1].split(".")[0]

                true_path = os.path.join(true_dir, f"mito_seg_{idx}.zst")
                if not os.path.exists(true_path):
                    true_path = os.path.join(true_dir, f"mito_seg_{idx}.npz")

                if not os.path.exists(true_path):
                    logger.warning(f"Ground truth not found for {file}")
                    continue

                files_to_evaluate.append(
                    (file, idx, surfix, os.path.join(pred_dir, file), true_path)
                )

        logger.info(f"Found {len(files_to_evaluate)} files to evaluate")

        # 处理所有文件
        for file, idx, surfix, pred_path, true_path in tqdm(
            files_to_evaluate, desc="Evaluating files"
        ):
            logger.info(f"Evaluating {file}...")
            metrics = evaluate_volume(pred_path, true_path)

            # 输出简单的指标信息
            logger.info(
                f"  IoU: {metrics['iou']:.4f}, Precision: {metrics['precision']:.4f}, Recall: {metrics['recall']:.4f}, Accuracy: {metrics['accuracy']:.4f}"
            )

            if idx not in metrics_all:
                metrics_all[idx] = {}
            metrics_all[idx][surfix] = metrics

        output_file = os.path.join(pred_dir, "metrics.json")

    # 计算平均指标
    avg_metrics = {}
    for idx, metrics_by_suffix in metrics_all.items():
        for _, metrics in metrics_by_suffix.items():  # 使用_忽略未使用的变量
            for metric_name, value in metrics.items():
                if isinstance(value, (int, float)):
                    if metric_name not in avg_metrics:
                        avg_metrics[metric_name] = []
                    avg_metrics[metric_name].append(value)

    # 计算平均值
    avg_results = {
        metric: float(np.mean(values)) for metric, values in avg_metrics.items()
    }
    metrics_all["average"] = avg_results

    # 将数据转换为可序列化的格式
    serializable_metrics = convert_to_json_serializable(metrics_all)

    # 保存结果
    with open(output_file, "w") as f:
        json.dump(serializable_metrics, f, indent=2)

    logger.info(f"Results saved to {output_file}")
    logger.info("Average metrics:")
    for metric, value in avg_results.items():
        logger.info(f"  {metric}: {value:.4f}")


if __name__ == "__main__":
    main()
