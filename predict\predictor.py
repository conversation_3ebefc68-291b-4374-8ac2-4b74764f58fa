import os
import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Any, Union
from dataprocess.volume import Volume
from predict.data_loader import (
    VolumeDataLoader,
    FewshotDataLoader,
    InferenceDataLoader,
)
from predict.model_manager import BaseModelManager, SAM2ModelManager, UNetModelManager
from predict.post_processor import (
    BasePostProcessor,
    InstanceSegmentationProcessor,
    DirectionMergeProcessor,
    MWSPostProcessor,
    GaussianBlurProcessor,
    WatershedPostProcessor,
    SDFPostProcessor,
)
from tqdm import tqdm


class BasePredictor:
    """
    预测器基类，负责协调数据加载和模型预测
    """

    def __init__(self, config_manager=None):
        """
        初始化预测器

        Args:
            config_manager: 配置管理器
        """
        self.config = config_manager
        self.data_loader = None
        self.model_manager = None
        self.post_processor = None
        self.resulotion = config_manager.get("task.resolution", 1024)
        self.work_device = config_manager.get(
            "task.device", "cuda" if torch.cuda.is_available() else "cpu"
        )

        if self.work_device == "cuda":
            torch.autocast("cuda", dtype=torch.bfloat16).__enter__()
            if torch.cuda.get_device_properties(0).major >= 8:
                torch.backends.cuda.matmul.allow_tf32 = True
                torch.backends.cudnn.allow_tf32 = True

    def predict(self, volume_path: str, output_dir: str, **kwargs) -> Any:
        """
        进行预测

        Args:
            volume_path: 体积数据路径
            output_dir: 输出目录
            **kwargs: 其他参数

        Returns:
            预测结果
        """
        raise NotImplementedError("Subclasses must implement predict")

    def save_result(
        self,
        result: Union[np.ndarray, torch.Tensor],
        volume_path: Union[str, Volume],
        output_dir: str,
        suffix: str,
        direction: str = "z",
        ori_resolution=None,
    ) -> None:
        """
        保存结果

        Args:
            result: 结果
            volume_path: 体积数据路径
            output_dir: 输出目录
            suffix: 文件名后缀
            direction: 方向，默认为 "z"
        """
        if isinstance(result, torch.Tensor):
            if result.dtype == torch.bfloat16:
                result = result.to(torch.float16)
            result = result.to("cpu").numpy()

        if isinstance(volume_path, Volume):
            volume_path = volume_path.volume_path

        out_mask = Volume(None)
        out_mask.volume = result

        # 只在方向是 x, y, z 时才旋转
        if direction in ["x", "y", "z"]:
            out_mask.rotate_volume(direction)

        # 缩放回原始分辨率
        if ori_resolution is not None:
            out_mask.scale_volume_to(ori_resolution)

        mask_name = os.path.basename(volume_path)
        pred_mask_name = f"{mask_name.split('.')[0]}_{suffix}.zst"
        out_mask.save_volume(os.path.join(output_dir, pred_mask_name))


class FewshotPredictor(BasePredictor):
    """
    Few-shot预测器，专门处理few-shot模式的预测
    """

    def __init__(self, config_manager=None):
        """
        初始化few-shot预测器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)
        self.data_loader = FewshotDataLoader(config_manager)
        self.model_manager = SAM2ModelManager(self.work_device, config_manager)
        self.model_manager.load_model()
        self.direction_processor = DirectionMergeProcessor(config_manager)
        self.blur_processor = GaussianBlurProcessor(config_manager)
        self.instance_processor = InstanceSegmentationProcessor(config_manager)
        self.sdf_processor = SDFPostProcessor(config_manager)
        self.unet_downsample = config_manager.get("task.unet_downsample", 2)
        self.to_sdf = config_manager.get("task.to_sdf", False)

    def predict(
        self,
        volume_path: Union[str, Volume],
        output_dir: str,
        masks_path: str = None,
        few_masks: dict = None,
        **kwargs,
    ) -> np.ndarray:
        """
        进行few-shot预测

        Args:
            volume_path: 体积数据路径
            output_dir: 输出目录
            masks_path: mask数据路径
            few_masks: few-shot mask字典
            **kwargs: 其他参数

        Returns:
            预测结果
        """
        # 加载体积
        volume = self.data_loader.load_volume(
            volume_path, to_memory=kwargs.get("keep_in_mem", False)
        )
        volume.scale_volume_to(tuple([self.resulotion] * 3))
        volume_mean = kwargs.get("mean", None)
        volume_std = kwargs.get("std", None)

        # 加载mask
        assert (
            masks_path is not None or few_masks is not None
        ), "masks_path or few_masks must be provided"
        if few_masks is None:
            masks = self.data_loader.load_mask(
                masks_path, to_memory=kwargs.get("keep_in_mem", False)
            )
            masks.scale_volume_to(tuple([self.resulotion] * 3))

        # 获取方向
        directions = kwargs.get("directions", ["x", "y", "z"])
        if directions is None:
            directions = ["x", "y", "z"]

        # 混合mask
        mixed_masks = None
        # 获取是否保存结果
        save_results = kwargs.get("save_results", True)

        # 对每个方向进行预测
        for direction in directions:
            self.model_manager.to_device(self.work_device)

            # 获取帧
            volume_frames, _ = self.data_loader.get_frames(volume, direction=direction)

            # 获取few-shot mask
            if few_masks is None:
                masks.volume_to_frames(direction=direction, use_pil=False)
                one_way_few_masks = self.data_loader.get_few_masks(
                    masks,
                    kwargs.get("label_start", 400),
                    kwargs.get("label_stride", 200),
                )
            else:
                one_way_few_masks = few_masks[direction]

            # 获取SAM预测器
            sam_predictor = self.model_manager.get_predictor()

            # 准备数据
            sam_predictor.load_volume(volume_frames, volume_mean, volume_std)
            sam_predictor.set_init_state(images_on_cpu=True)

            # 添加few-shot提示
            label_start = kwargs.get("label_start", 400)
            for frame_idx, mask in one_way_few_masks.items():
                sam_predictor.add_mask_prompt(frame_idx=frame_idx, obj_id=0, mask=mask)
            sam_predictor.set_start_frame(label_start)
            sam_predictor.add_prompts_to_state()

            # 预测
            sam_predictor.predict_single(twice=False)
            mask = sam_predictor.get_id_masks(
                to_binary=kwargs.get("mask_to_binary", True), to_cpu=False
            )

            # 清理
            sam_predictor.reset_state()
            sam_predictor.clear()
            self.model_manager.clear_cache()

            # 保存结果

            if save_results and kwargs.get("save_xyz_masks", True):
                self.save_result(mask, volume_path, output_dir, direction, direction)

            # 混合mask
            self.model_manager.to_device("cpu")
            self.model_manager.clear_cache()
            # 确保不是布尔类型
            if mask.dtype == torch.bool:
                mask = mask.float()
            mixed_masks = self.direction_processor.process(
                mask, direction, mixed_masks, self.work_device
            )

        # 高斯模糊
        mixed_masks = self.blur_processor.process(mixed_masks, device=self.work_device)
        # 生成SDF
        if self.to_sdf:
            if self.unet_downsample > 1:
                downsample_resolution = tuple(
                    [self.resulotion // self.unet_downsample] * 3
                )
                downsample = True
            else:
                downsample = False
                downsample_resolution = self.resulotion

            mixed_masks = mixed_masks > 0
            mixed_masks = self.sdf_processor.process(
                mixed_masks,
                donwsample=downsample,
                resolution=downsample_resolution,
            )

        # 保存混合结果（添加条件判断）

        if save_results and mixed_masks is not None:
            self.save_result(mixed_masks, volume_path, output_dir, "avg", "z")

        return mixed_masks


class InferencePredictor(BasePredictor):
    """
    推理预测器，专门处理推理模式的预测
    """

    def __init__(self, config_manager=None):
        """
        初始化推理预测器

        Args:
            config_manager: 配置管理器
        """
        super().__init__(config_manager)
        self.unet_downsample = config_manager.get("task.unet_downsample", 2)
        self.ws_downsample = config_manager.get("task.ws_downsample", 2)
        self.data_loader = InferenceDataLoader(config_manager)
        self.sam_manager = SAM2ModelManager(self.work_device, config_manager)
        self.sam_manager.load_model()
        self.unet_manager = UNetModelManager(self.work_device, config_manager)
        self.unet_manager.load_model()
        # self.instance_processor = InstanceSegmentationProcessor(config_manager)
        self.instance_processor = MWSPostProcessor(config_manager)
        self.watershed_processor = WatershedPostProcessor(config_manager)

    def predict(
        self,
        volume_path: str,
        output_dir: str,
        ref_masks_path: Optional[str] = None,
        **kwargs,
    ) -> np.ndarray:
        """
        进行推理预测

        Args:
            volume_path: 体积数据路径
            output_dir: 输出目录
            ref_masks_path: 参考mask数据路径
            **kwargs: 其他参数

        Returns:
            预测结果
        """
        # 加载体积
        volume = self.data_loader.load_volume(
            volume_path, to_memory=kwargs.get("keep_in_mem", False)
        )
        ori_resolution = volume.get_resolution()
        volume_mean = kwargs.get("mean", None)
        volume_std = kwargs.get("std", None)

        # 如果没有参考mask，使用UNet生成
        ref_masks_in_mem = None
        if ref_masks_path is None:
            # 切换到UNet模型
            if kwargs.get("switch_model", True):
                self.unet_manager.to_device(self.work_device)

            # 为UNet准备数据
            unet_resolution = tuple([self.resulotion // self.unet_downsample] * 3)
            volume_downsampled = volume.scale_volume_to(unet_resolution, copy=True)

            # 获取UNet预测器和配置
            unet_predictor = self.unet_manager.get_predictor()
            unet_config = self.unet_manager.get_config()

            # 获取测试加载器
            from pytorch3dunet.datasets.utils import get_test_loaders

            test_loader = next(
                get_test_loaders(
                    unet_config,
                    volume_downsampled,
                    volume_mean=volume_mean,
                    volume_std=volume_std,
                )
            )

            # 预测
            mask = unet_predictor(test_loader)

            # 创建参考mask
            ref_masks_in_mem = Volume(None)
            ref_masks_in_mem.volume = mask

            # 切换回SAM模型并清理
            if kwargs.get("switch_model", True):
                self.unet_manager.to_device("cpu")
                self.sam_manager.to_device(self.work_device)
            self.unet_manager.cleanup()
        else:
            # 加载参考mask
            ref_masks_in_mem = self.data_loader.load_ref_mask(
                ref_masks_path, to_memory=kwargs.get("keep_in_mem", False)
            )

        # 设置SAM内存类型
        sam_predictor = self.sam_manager.get_predictor()
        sam_predictor.set_memory_type("bidirectional")

        # 缩放volume
        volume.scale_volume_to(tuple([self.resulotion] * 3))

        # 获取方向
        directions = kwargs.get("directions", ["z"])
        if directions is None:
            directions = ["z"]

        # 对每个方向进行预测
        for direction in directions:
            # 获取帧
            volume_frames, _ = self.data_loader.get_frames(volume, direction=direction)

            # 获取用于watershed的参考sdf
            ref_masks_in_mem.volume_to_frames(direction=direction, use_pil=False)
            ref_masks_ori = ref_masks_in_mem.frames

            # 缩放参考mask，距离场需要按照UNet降采样的倍率放大
            ref_masks_in_mem.scale_volume_to(tuple([self.resulotion] * 3))
            ref_masks_in_mem.volume *= self.unet_downsample
            ref_masks_in_mem.volume_to_frames(direction=direction, use_pil=False)

            # 准备数据
            sam_predictor.load_volume(volume_frames, volume_mean, volume_std)
            sam_predictor.set_init_state(images_on_cpu=True)

            # 添加inference提示
            sam_predictor.add_ref_masks(obj_id=0, ref_masks=ref_masks_in_mem.frames)

            # 预测
            sam_predictor.predict_single(twice=True)
            mask = sam_predictor.get_id_masks(
                to_binary=kwargs.get("mask_to_binary", True), to_cpu=False
            )

            # 清理
            self.sam_manager.cleanup()

            # 实例分割
            if kwargs.get("to_instance", False):
                if False:
                    # mask 缩放
                    resulotion = self.resulotion // 2
                    mask = mask.unsqueeze(0).unsqueeze(0)
                    mask = torch.nn.functional.interpolate(
                        mask,
                        size=(resulotion, resulotion, resulotion),
                        mode="trilinear",
                    ).to("cpu")
                    mask = mask.squeeze(0).squeeze(0)
                    img = volume_downsampled.astype(np.float32) / 255.0
                    img = torch.from_numpy(img)
                    torch.cuda.empty_cache()
                    mask = self.instance_processor.process(img, mask, self.work_device)
                else:
                    resulotion = self.resulotion // self.ws_downsample
                    mask = mask.cpu().numpy()
                    mask_downsampled = mask[::2, ::2, ::2]
                    mask = self.watershed_processor.process(
                        mask, mask_downsampled, ref_masks_ori
                    )

            # 保存结果
            if kwargs.get("save_masks", True):
                self.save_result(
                    mask, volume_path, output_dir, direction, direction, ori_resolution
                )

        if kwargs.get("switch_model", True):
            self.sam_manager.to_device("cpu")

        return mask
