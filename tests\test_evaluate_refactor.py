#!/usr/bin/env python3
"""
Test script for the refactored evaluate.py module.
Tests the new get_evaluation_files function and data structure changes.
"""

import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock
import sys

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from predict.evaluate import get_evaluation_files


class TestEvaluateRefactor(unittest.TestCase):
    """Test cases for the refactored evaluate.py module."""

    def setUp(self):
        """Set up test directories and files."""
        # Create temporary directories
        self.temp_dir = tempfile.mkdtemp()
        self.pred_dir = os.path.join(self.temp_dir, "pred")
        self.true_dir = os.path.join(self.temp_dir, "true")
        
        os.makedirs(self.pred_dir)
        os.makedirs(self.true_dir)

    def tearDown(self):
        """Clean up test directories."""
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_get_evaluation_files_specific_format(self):
        """Test get_evaluation_files with specific format (mito_seg)."""
        # Create test files in specific format
        pred_files = ["pred_001_gaussian.zst", "pred_002_gaussian.npz"]
        true_files = ["mito_seg_001.zst", "mito_seg_002.npz"]
        
        # Create prediction files
        for file in pred_files:
            with open(os.path.join(self.pred_dir, file), 'w') as f:
                f.write("dummy")
        
        # Create ground truth files
        for file in true_files:
            with open(os.path.join(self.true_dir, file), 'w') as f:
                f.write("dummy")
        
        # Test specific format
        result = get_evaluation_files(self.pred_dir, self.true_dir, use_specific_format=True)
        
        # Verify results
        self.assertEqual(len(result), 2)
        
        # Check first file
        self.assertEqual(result[0]["idx"], "001")
        self.assertEqual(result[0]["surfix"], "gaussian")
        self.assertTrue(result[0]["pred_path"].endswith("pred_001_gaussian.zst"))
        self.assertTrue(result[0]["true_path"].endswith("mito_seg_001.zst"))
        
        # Check second file
        self.assertEqual(result[1]["idx"], "002")
        self.assertEqual(result[1]["surfix"], "gaussian")
        self.assertTrue(result[1]["pred_path"].endswith("pred_002_gaussian.npz"))
        self.assertTrue(result[1]["true_path"].endswith("mito_seg_002.npz"))

    def test_get_evaluation_files_generic_format(self):
        """Test get_evaluation_files with generic format."""
        # Create test files in generic format
        pred_files = ["file1.zst", "file2.npz", "file3.zst"]
        true_files = ["gt1.zst", "gt2.npz", "gt3.zst"]
        
        # Create prediction files
        for file in pred_files:
            with open(os.path.join(self.pred_dir, file), 'w') as f:
                f.write("dummy")
        
        # Create ground truth files
        for file in true_files:
            with open(os.path.join(self.true_dir, file), 'w') as f:
                f.write("dummy")
        
        # Test generic format
        result = get_evaluation_files(self.pred_dir, self.true_dir, use_specific_format=False)
        
        # Verify results
        self.assertEqual(len(result), 3)
        
        # Check that idx and surfix are None for generic format
        for item in result:
            self.assertIsNone(item["idx"])
            self.assertIsNone(item["surfix"])
            self.assertIsNotNone(item["pred_path"])
            self.assertIsNotNone(item["true_path"])
        
        # Check file pairing (should be sorted)
        self.assertTrue(result[0]["pred_path"].endswith("file1.zst"))
        self.assertTrue(result[0]["true_path"].endswith("gt1.zst"))
        self.assertTrue(result[1]["pred_path"].endswith("file2.npz"))
        self.assertTrue(result[1]["true_path"].endswith("gt2.npz"))

    def test_get_evaluation_files_mismatched_count(self):
        """Test get_evaluation_files when pred and true file counts don't match."""
        # Create mismatched files
        pred_files = ["file1.zst", "file2.npz"]
        true_files = ["gt1.zst", "gt2.npz", "gt3.zst"]  # One extra file
        
        # Create prediction files
        for file in pred_files:
            with open(os.path.join(self.pred_dir, file), 'w') as f:
                f.write("dummy")
        
        # Create ground truth files
        for file in true_files:
            with open(os.path.join(self.true_dir, file), 'w') as f:
                f.write("dummy")
        
        # Test generic format with mismatched counts
        with patch('predict.evaluate.logger') as mock_logger:
            result = get_evaluation_files(self.pred_dir, self.true_dir, use_specific_format=False)
            
            # Should log a warning
            mock_logger.warning.assert_called_once()
            
            # Should return the minimum count
            self.assertEqual(len(result), 2)

    def test_get_evaluation_files_missing_ground_truth(self):
        """Test get_evaluation_files when ground truth files are missing."""
        # Create prediction files
        pred_files = ["pred_001_gaussian.zst", "pred_002_gaussian.npz"]
        for file in pred_files:
            with open(os.path.join(self.pred_dir, file), 'w') as f:
                f.write("dummy")
        
        # Create only one ground truth file (missing one)
        with open(os.path.join(self.true_dir, "mito_seg_001.zst"), 'w') as f:
            f.write("dummy")
        
        # Test specific format with missing ground truth
        with patch('predict.evaluate.logger') as mock_logger:
            result = get_evaluation_files(self.pred_dir, self.true_dir, use_specific_format=True)
            
            # Should log a warning for missing ground truth
            mock_logger.warning.assert_called()
            
            # Should return only the file with existing ground truth
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0]["idx"], "001")


if __name__ == '__main__':
    unittest.main()
